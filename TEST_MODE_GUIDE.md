# 测试模式使用指南

## 概述

为了方便测试，项目现在支持**自动清空数据库**的测试模式。每次运行测试前，都会自动：

1. 🗑️ 清空所有表数据
2. 🏗️ 重新创建表结构（如果需要）
3. 📚 初始化朝代数据
4. 🕷️ 开始爬取测试

## 测试方式

### 1. 完整测试（推荐）

```bash
python test_crawler.py
```

**特点：**
- 完整的测试流程和日志
- 支持多个URL测试
- 详细的统计信息
- 美观的输出格式

**输出示例：**
```
==================================================
🚀 启动测试爬虫
==================================================
🗑️  清空数据库...
🏗️  重新创建表结构...
📚 初始化朝代数据...
✅ 数据库重置完成

📊 数据库状态:
  dynasties: 12 条记录
  authors: 0 条记录
  tags: 0 条记录
  poems: 0 条记录
  ...

📝 测试 1/1
🕷️  开始爬取: http://shangshiwen.com/67827.html
🎉 爬取成功: 白头吟 - 卓文君

==================================================
📈 测试结果统计
==================================================
总计: 1 个URL
成功: 1 个
失败: 0 个
成功率: 100.0%
```

### 2. 快速测试

```bash
# 测试默认URL
python quick_test.py

# 测试指定URL
python quick_test.py http://shangshiwen.com/67827.html
```

**特点：**
- 简洁快速
- 适合单个URL测试
- 最少的输出信息

**输出示例：**
```
🔄 重置数据库...
🕷️  测试爬取: http://shangshiwen.com/67827.html
✅ 成功: 白头吟 - 卓文君

📊 数据库状态:
  dynasties: 12 条记录
  authors: 1 条记录
  tags: 13 条记录
  poems: 1 条记录
  poem_tags: 13 条记录
```

### 3. 主程序测试

```bash
python main.py
```

**特点：**
- 使用主程序入口
- 自动清空数据库
- 完整的日志记录

### 4. 单独爬虫测试

```bash
python shangshiwen_spider.py
```

**特点：**
- 直接运行爬虫模块
- 包含数据库重置
- 适合调试爬虫逻辑

## 添加更多测试URL

编辑 `test_crawler.py` 文件，在 `test_urls` 列表中添加更多URL：

```python
# 测试URL列表
test_urls = [
    "http://shangshiwen.com/67827.html",  # 白头吟 - 卓文君
    "http://shangshiwen.com/12345.html",  # 添加更多测试URL
    "http://shangshiwen.com/67890.html",  # 添加更多测试URL
]
```

## 手动数据库操作

如果需要手动操作数据库：

```python
from utils.db_utils import clear_tables, reset_database, get_table_counts

# 只清空数据
clear_tables()

# 完整重置（清空+初始化）
reset_database()

# 查看数据库状态
counts = get_table_counts()
print(counts)
```

## 数据库状态说明

测试完成后会显示各表的记录数：

- **dynasties**: 朝代表（固定12条记录）
- **authors**: 作者表（根据爬取的诗词动态增加）
- **tags**: 标签表（根据爬取的诗词动态增加）
- **poems**: 诗词表（每个成功爬取的诗词1条记录）
- **poem_tags**: 诗词标签关联表（每个诗词-标签关系1条记录）
- **translations**: 翻译表（暂未实现，为0）
- **appreciations**: 赏析表（暂未实现，为0）

## 注意事项

1. **数据会被清空**：每次测试都会清空所有数据，请确保不在生产环境使用
2. **朝代数据自动恢复**：清空后会自动重新初始化12个朝代
3. **网络延迟**：爬取过程需要网络请求，请耐心等待
4. **错误处理**：如果爬取失败，会显示详细的错误信息

## 开发建议

1. **开发新功能时**：使用 `quick_test.py` 快速验证
2. **完整测试时**：使用 `test_crawler.py` 进行全面测试
3. **调试爬虫逻辑时**：直接运行 `shangshiwen_spider.py`
4. **批量测试时**：在 `test_crawler.py` 中添加多个URL

这样的测试模式确保每次测试都从干净的状态开始，避免了数据污染和重复数据的问题。

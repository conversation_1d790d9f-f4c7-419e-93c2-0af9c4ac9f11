#!/usr/bin/env python3
"""
测试代理获取功能
"""
import time
import requests
from loguru import logger
from utils.proxy_manager import ProxyManager, FreeProxyFetcher


def test_free_proxy_sources():
    """测试多个免费代理源"""
    logger.info("=== 测试免费代理获取 ===")
    
    # 测试方法1: proxy-list.download
    logger.info("1. 测试 proxy-list.download...")
    try:
        proxies1 = FreeProxyFetcher.fetch_from_proxylist()
        logger.info(f"获取到 {len(proxies1)} 个代理")
        if proxies1:
            for i, proxy in enumerate(proxies1[:3]):  # 显示前3个
                logger.info(f"  代理{i+1}: {proxy['host']}:{proxy['port']}")
    except Exception as e:
        logger.error(f"获取失败: {e}")
    
    # 测试方法2: 其他免费代理源
    logger.info("\n2. 测试其他免费代理源...")
    other_proxies = test_other_proxy_sources()
    
    return proxies1 + other_proxies if 'proxies1' in locals() else other_proxies


def test_other_proxy_sources():
    """测试其他免费代理源"""
    proxies = []
    
    # 代理源1: free-proxy-list.net
    try:
        logger.info("测试 free-proxy-list.net...")
        url = "https://free-proxy-list.net/"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            # 简单解析（实际需要用BeautifulSoup解析表格）
            content = response.text
            if 'proxy' in content.lower():
                logger.info("✅ free-proxy-list.net 可访问")
                # 这里可以添加具体的解析逻辑
            else:
                logger.warning("⚠️ free-proxy-list.net 内容异常")
        else:
            logger.error(f"❌ free-proxy-list.net 访问失败: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ free-proxy-list.net 异常: {e}")
    
    # 代理源2: proxylist.geonode.com
    try:
        logger.info("测试 proxylist.geonode.com...")
        url = "https://proxylist.geonode.com/api/proxy-list?limit=10&page=1&sort_by=lastChecked&sort_type=desc"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data:
                proxy_list = data['data']
                logger.info(f"✅ 获取到 {len(proxy_list)} 个代理")
                
                for proxy_data in proxy_list[:5]:  # 取前5个
                    proxy = {
                        'host': proxy_data['ip'],
                        'port': int(proxy_data['port']),
                        'protocol': proxy_data['protocols'][0] if proxy_data['protocols'] else 'http'
                    }
                    proxies.append(proxy)
                    logger.info(f"  代理: {proxy['host']}:{proxy['port']} ({proxy['protocol']})")
            else:
                logger.warning("⚠️ geonode 数据格式异常")
        else:
            logger.error(f"❌ geonode 访问失败: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ geonode 异常: {e}")
    
    # 代理源3: 手动添加一些公开代理进行测试
    logger.info("添加一些测试代理...")
    test_proxies = [
        {'host': '***********', 'port': 80, 'protocol': 'http'},
        {'host': '************', 'port': 8888, 'protocol': 'http'},
        {'host': '***************', 'port': 80, 'protocol': 'http'},
    ]
    
    for proxy in test_proxies:
        proxies.append(proxy)
        logger.info(f"  测试代理: {proxy['host']}:{proxy['port']}")
    
    return proxies


def test_proxy_manager():
    """测试代理管理器功能"""
    logger.info("\n=== 测试代理管理器 ===")
    
    # 创建代理管理器
    manager = ProxyManager()
    
    # 获取免费代理
    logger.info("获取免费代理...")
    free_proxies = test_free_proxy_sources()
    
    if free_proxies:
        # 加载代理
        manager.load_proxies(free_proxies[:10])  # 只加载前10个
        
        # 显示代理统计
        stats = manager.get_stats()
        logger.info(f"代理统计: {stats}")
        
        # 测试代理可用性
        logger.info("检查代理可用性...")
        results = manager.check_all_proxies(max_workers=5)
        logger.info(f"检查结果: {results}")
        
        # 显示更新后的统计
        stats = manager.get_stats()
        logger.info(f"更新后统计: {stats}")
        
        # 测试获取代理
        if stats['active_proxies'] > 0:
            logger.info("\n测试代理轮换:")
            for i in range(5):
                proxy = manager.get_proxy('round_robin')
                if proxy:
                    logger.info(f"  轮换{i+1}: {proxy.host}:{proxy.port}")
                else:
                    logger.warning("  没有可用代理")
        else:
            logger.warning("没有可用的代理")
    else:
        logger.error("没有获取到任何代理")


def test_proxy_request():
    """测试使用代理发起请求"""
    logger.info("\n=== 测试代理请求 ===")
    
    manager = ProxyManager()
    
    # 获取一些代理
    free_proxies = test_free_proxy_sources()
    if free_proxies:
        manager.load_proxies(free_proxies[:5])
        manager.check_all_proxies(max_workers=3)
        
        # 获取一个可用代理
        proxy = manager.get_proxy()
        if proxy:
            logger.info(f"使用代理: {proxy.host}:{proxy.port}")
            
            # 测试请求
            try:
                proxies = {
                    'http': proxy.url,
                    'https': proxy.url
                }
                
                response = requests.get(
                    'http://httpbin.org/ip',
                    proxies=proxies,
                    timeout=10,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.success(f"✅ 代理请求成功! IP: {data.get('origin', 'unknown')}")
                    manager.mark_success(proxy, response.elapsed.total_seconds())
                else:
                    logger.error(f"❌ 代理请求失败: {response.status_code}")
                    manager.mark_failure(proxy)
                    
            except Exception as e:
                logger.error(f"❌ 代理请求异常: {e}")
                manager.mark_failure(proxy)
        else:
            logger.warning("没有可用的代理进行测试")
    else:
        logger.error("没有获取到代理")


def test_proxy_rotation():
    """测试代理轮换策略"""
    logger.info("\n=== 测试代理轮换策略 ===")
    
    manager = ProxyManager()
    
    # 添加一些测试代理
    test_proxies = [
        {'host': '127.0.0.1', 'port': 8080, 'protocol': 'http'},
        {'host': '127.0.0.1', 'port': 8081, 'protocol': 'http'},
        {'host': '127.0.0.1', 'port': 8082, 'protocol': 'http'},
    ]
    
    manager.load_proxies(test_proxies)
    
    # 测试不同轮换策略
    strategies = ['round_robin', 'random', 'best_performance']
    
    for strategy in strategies:
        logger.info(f"\n测试策略: {strategy}")
        for i in range(5):
            proxy = manager.get_proxy(strategy)
            if proxy:
                logger.info(f"  {i+1}: {proxy.host}:{proxy.port}")


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}\n"
    )
    
    try:
        # 运行所有测试
        test_free_proxy_sources()
        test_proxy_manager()
        test_proxy_request()
        test_proxy_rotation()
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试异常: {e}")
    finally:
        logger.info("测试完成")

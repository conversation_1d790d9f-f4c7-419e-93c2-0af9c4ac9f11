#!/usr/bin/env python3
"""
快速测试脚本 - 清空数据库并测试单个URL
"""
from utils.db_utils import reset_database, get_table_counts
from shangshiwen_spider import <PERSON>gShiWenSpider


def quick_test(url="http://shangshiwen.com/67827.html", include_tags=False):
    """快速测试单个URL"""
    print("🔄 重置数据库...")
    reset_database()

    spider = ShangShiWenSpider()

    # 可选：爬取标签
    if include_tags:
        print("🏷️  爬取标签...")
        tags_data = spider.crawl_all_tags()
        print(f"标签: {len(tags_data)} 个")

    print(f"🕷️  测试爬取: {url}")
    result = spider.crawl_poem(url)

    if result:
        print(f"✅ 成功: {result['title']} - {result['author_name']}")

        # 显示数据库状态
        print("\n📊 数据库状态:")
        counts = get_table_counts()
        for table, count in counts.items():
            if count > 0:
                print(f"  {table}: {count} 条记录")
    else:
        print("❌ 失败")


if __name__ == "__main__":
    import sys

    # 解析命令行参数
    url = "http://shangshiwen.com/67827.html"
    include_tags = False

    for arg in sys.argv[1:]:
        if arg == "--tags":
            include_tags = True
        elif arg.startswith("http"):
            url = arg

    quick_test(url, include_tags)

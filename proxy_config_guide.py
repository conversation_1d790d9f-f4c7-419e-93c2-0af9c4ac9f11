#!/usr/bin/env python3
"""
代理配置指南 - 提供多种代理配置方案
"""
import os
from loguru import logger
from utils.proxy_manager import ProxyManager, FreeProxyFetcher
from enhanced_spider_simple import SimpleEnhancedSpider


class ProxyConfigGuide:
    """代理配置指南"""
    
    @staticmethod
    def test_no_proxy():
        """测试无代理模式"""
        logger.info("=== 测试无代理模式 ===")
        
        spider = SimpleEnhancedSpider()
        
        try:
            test_url = "http://shangshiwen.com/67827.html"
            result = spider.crawl_poem_with_stats(test_url)
            
            if result:
                logger.success(f"✅ 无代理爬取成功: {result['title']}")
                return True
            else:
                logger.error("❌ 无代理爬取失败")
                return False
                
        except Exception as e:
            logger.error(f"无代理爬取异常: {e}")
            return False
        finally:
            spider.print_stats()
    
    @staticmethod
    def test_local_proxy():
        """测试本地代理"""
        logger.info("=== 测试本地代理 ===")
        
        # 检查常见的本地代理端口
        local_proxies = [
            {'host': '127.0.0.1', 'port': 8080, 'protocol': 'http'},
            {'host': '127.0.0.1', 'port': 1080, 'protocol': 'http'},
            {'host': '127.0.0.1', 'port': 7890, 'protocol': 'http'},  # Clash默认端口
            {'host': '127.0.0.1', 'port': 8888, 'protocol': 'http'},
        ]
        
        manager = ProxyManager()
        manager.load_proxies(local_proxies)
        
        logger.info("检查本地代理可用性...")
        results = manager.check_all_proxies(max_workers=2)
        
        stats = manager.get_stats()
        if stats['active_proxies'] > 0:
            logger.success(f"✅ 发现 {stats['active_proxies']} 个可用的本地代理")
            
            # 测试使用本地代理
            proxy = manager.get_proxy()
            if proxy:
                logger.info(f"测试代理: {proxy.host}:{proxy.port}")
                
                try:
                    import requests
                    proxies = {'http': proxy.url, 'https': proxy.url}
                    response = requests.get(
                        'http://httpbin.org/ip',
                        proxies=proxies,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        logger.success(f"✅ 本地代理测试成功! IP: {data.get('origin', 'unknown')}")
                        return True
                    else:
                        logger.error(f"❌ 本地代理测试失败: {response.status_code}")
                        
                except Exception as e:
                    logger.error(f"❌ 本地代理测试异常: {e}")
        else:
            logger.warning("⚠️ 没有发现可用的本地代理")
        
        return False
    
    @staticmethod
    def show_proxy_setup_guide():
        """显示代理设置指南"""
        logger.info("=== 代理设置指南 ===")
        
        print("""
🔧 代理配置方案:

1. 【推荐】本地代理软件:
   - Clash for Windows/Mac: 端口通常是 7890
   - V2rayN: 端口通常是 10809
   - Shadowsocks: 端口通常是 1080
   
   设置方法:
   - 启动代理软件
   - 确保允许局域网连接
   - 使用 127.0.0.1:端口号

2. 付费代理服务:
   - 亮数据 (Bright Data)
   - ProxyMesh
   - Smartproxy
   
   配置示例:
   ```python
   premium_proxies = [
       {
           'host': 'proxy.example.com',
           'port': 8080,
           'username': 'your_username',
           'password': 'your_password',
           'protocol': 'http'
       }
   ]
   ```

3. 免费代理 (不推荐生产使用):
   - 质量不稳定
   - 速度较慢
   - 可能存在安全风险
   
4. 环境变量配置:
   ```bash
   export PROXY_ENABLED=true
   export PROXY_HOST=127.0.0.1
   export PROXY_PORT=7890
   ```

5. 代码中配置:
   ```python
   from proxy_enhanced_spider import ProxyEnhancedSpider
   
   # 使用自定义代理
   custom_proxies = [
       {'host': '127.0.0.1', 'port': 7890, 'protocol': 'http'}
   ]
   
   spider = ProxyEnhancedSpider(
       enable_proxy=True,
       proxy_sources=custom_proxies
   )
   ```
        """)
    
    @staticmethod
    def create_proxy_config_file():
        """创建代理配置文件"""
        logger.info("=== 创建代理配置文件 ===")
        
        config_content = """# 代理配置文件
# 请根据你的实际情况修改

# 本地代理配置 (推荐)
LOCAL_PROXIES = [
    {'host': '127.0.0.1', 'port': 7890, 'protocol': 'http'},   # Clash
    {'host': '127.0.0.1', 'port': 10809, 'protocol': 'http'},  # V2rayN
    {'host': '127.0.0.1', 'port': 1080, 'protocol': 'http'},   # Shadowsocks
]

# 付费代理配置
PREMIUM_PROXIES = [
    {
        'host': 'your-proxy-server.com',
        'port': 8080,
        'username': 'your_username',
        'password': 'your_password',
        'protocol': 'http'
    }
]

# 使用示例:
# from proxy_config import LOCAL_PROXIES
# from proxy_enhanced_spider import ProxyEnhancedSpider
# 
# spider = ProxyEnhancedSpider(
#     enable_proxy=True,
#     proxy_sources=LOCAL_PROXIES
# )
"""
        
        config_file = "proxy_config.py"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        logger.success(f"✅ 代理配置文件已创建: {config_file}")
        logger.info("请编辑该文件，添加你的代理配置")
    
    @staticmethod
    def test_environment_proxy():
        """测试环境变量代理"""
        logger.info("=== 测试环境变量代理 ===")
        
        # 检查环境变量
        proxy_host = os.getenv('PROXY_HOST')
        proxy_port = os.getenv('PROXY_PORT')
        
        if proxy_host and proxy_port:
            logger.info(f"发现环境变量代理: {proxy_host}:{proxy_port}")
            
            proxy_config = [{
                'host': proxy_host,
                'port': int(proxy_port),
                'protocol': 'http'
            }]
            
            manager = ProxyManager()
            manager.load_proxies(proxy_config)
            
            results = manager.check_all_proxies()
            stats = manager.get_stats()
            
            if stats['active_proxies'] > 0:
                logger.success("✅ 环境变量代理可用")
                return True
            else:
                logger.error("❌ 环境变量代理不可用")
                return False
        else:
            logger.info("未设置环境变量代理 (PROXY_HOST, PROXY_PORT)")
            return False
    
    @staticmethod
    def run_all_tests():
        """运行所有代理测试"""
        logger.info("🚀 开始代理配置测试...")
        
        results = {
            'no_proxy': ProxyConfigGuide.test_no_proxy(),
            'local_proxy': ProxyConfigGuide.test_local_proxy(),
            'env_proxy': ProxyConfigGuide.test_environment_proxy()
        }
        
        logger.info("\n📊 测试结果汇总:")
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name}: {status}")
        
        # 给出建议
        if results['local_proxy']:
            logger.success("🎉 建议使用本地代理模式")
        elif results['no_proxy']:
            logger.info("💡 建议使用无代理模式")
        else:
            logger.warning("⚠️ 建议配置代理软件或使用付费代理")
        
        return results


def main():
    """主函数"""
    logger.remove()
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}\n"
    )
    
    try:
        # 显示代理设置指南
        ProxyConfigGuide.show_proxy_setup_guide()
        
        # 创建配置文件
        ProxyConfigGuide.create_proxy_config_file()
        
        # 运行测试
        ProxyConfigGuide.run_all_tests()
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试异常: {e}")
    finally:
        logger.info("测试完成")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
查看标签数据脚本
"""
from models import SessionLocal, PoemTag
from utils.db_utils import get_table_counts


def view_tags_summary():
    """查看标签数据摘要"""
    print("=" * 60)
    print("🏷️  标签数据摘要")
    print("=" * 60)
    
    # 显示数据库状态
    counts = get_table_counts()
    print("📊 数据库状态:")
    for table, count in counts.items():
        if count > 0:
            print(f"  {table}: {count:,} 条记录")
    
    db = SessionLocal()
    try:
        # 获取所有标签
        tags = db.query(PoemTag).order_by(PoemTag.tag_id).all()
        
        print(f"\n🏷️  共有 {len(tags)} 个标签")
        
        # 显示前20个标签
        print("\n📋 前20个标签:")
        for i, tag in enumerate(tags[:20], 1):
            print(f"  {i:2d}. ID: {tag.tag_id:3d} | {tag.name}")
        
        if len(tags) > 20:
            print(f"  ... 还有 {len(tags) - 20} 个标签")
        
        # 显示标签ID范围
        tag_ids = [tag.tag_id for tag in tags]
        print(f"\n📈 标签ID范围: {min(tag_ids)} - {max(tag_ids)}")
        print(f"📊 标签ID总数: {len(set(tag_ids))} 个唯一ID")
        
        # 检查是否有重复的标签ID
        if len(tag_ids) != len(set(tag_ids)):
            print("⚠️  发现重复的标签ID")
        else:
            print("✅ 所有标签ID都是唯一的")
            
    finally:
        db.close()


def search_tags(keyword):
    """搜索包含关键词的标签"""
    print(f"\n🔍 搜索包含 '{keyword}' 的标签:")
    
    db = SessionLocal()
    try:
        tags = db.query(PoemTag).filter(PoemTag.name.contains(keyword)).all()
        
        if tags:
            print(f"找到 {len(tags)} 个匹配的标签:")
            for tag in tags:
                print(f"  ID: {tag.tag_id:3d} | {tag.name}")
        else:
            print("  没有找到匹配的标签")
            
    finally:
        db.close()


def main():
    """主函数"""
    view_tags_summary()
    
    # 搜索一些常见的标签类型
    keywords = ["诗", "描写", "爱情", "春天"]
    for keyword in keywords:
        search_tags(keyword)
    
    print("\n" + "=" * 60)
    print("✅ 标签数据查看完成")
    print("💡 提示: 标签数据来源于 http://shangshiwen.com/tags.html")
    print("=" * 60)


if __name__ == "__main__":
    main()

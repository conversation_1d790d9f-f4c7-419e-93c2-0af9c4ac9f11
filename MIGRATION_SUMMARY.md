# 项目迁移总结 - 从古诗文网到尚诗文网

## 项目概述

本项目已成功从原来的古诗文网（gushiwen.cn）迁移到尚诗文网（shangshiwen.com），并重新设计了数据库结构以更好地适应新网站的数据特点。

## 主要变更

### 1. 目标网站更改
- **原网站**: https://www.gushiwen.cn
- **新网站**: http://shangshiwen.com
- **测试页面**: http://shangshiwen.com/67827.html (白头吟 - 卓文君)

### 2. 数据库结构重新设计

#### 新增表结构：

**dynasties 表（朝代表）**
- 解决了朝代管理问题
- 预置了12个朝代：先秦、两汉、魏晋、南北朝、隋代、唐朝、五代、宋代、金朝、元代、明代、清代
- 便于后续查询和管理

**tags 表（标签表）**
- 存储诗词类型标签（如：女子、爱情、山水诗等）
- 支持灵活的标签分类

**poem_tags 表（诗词标签关联表）**
- 实现诗词与标签的多对多关系
- 一个诗词可以有多个标签

**translations 表（翻译表）**
- 存储诗词的翻译内容
- 支持一个诗词有多个翻译版本
- 记录翻译来源URL

**appreciations 表（赏析表）**
- 存储诗词的赏析内容
- 支持一个诗词有多个赏析文章
- 记录赏析来源URL

#### 修改的表结构：

**poems 表**
- 添加 `poem_id` 字段：存储网站的诗词ID（如67827）
- 修改 `dynasty_id` 为外键关联dynasties表
- 修改 `author_id` 为外键关联authors表
- 移除 `translation`, `annotation`, `appreciation` 字段（单独建表）
- `content` 字段支持HTML格式存储

**authors 表**
- 添加 `author_id` 字段：存储网站的作者ID（如785）
- 添加 `dynasty_id` 外键关联dynasties表

### 3. 新爬虫实现

创建了 `shangshiwen_spider.py`，实现了以下功能：

#### 页面解析能力：
- ✅ 诗词ID提取（从URL中提取数字ID）
- ✅ 诗词标题解析
- ✅ 朝代信息提取
- ✅ 作者信息提取（姓名和ID）
- ✅ 标签信息提取（支持多个标签）
- ✅ 原文内容提取（保持HTML格式）
- ✅ 翻译链接识别
- ✅ 赏析链接识别

#### 数据处理能力：
- ✅ 自动创建朝代记录
- ✅ 自动创建标签记录
- ✅ 自动创建作者记录
- ✅ 建立诗词与标签的关联关系
- ✅ 避免重复数据插入

### 4. 配置文件更新

更新了 `config.py`：
```python
TARGET_SITES = {
    'shangshiwen': {
        'base_url': 'http://shangshiwen.com',
        'name': '尚诗文网',
        'poem_detail_pattern': r'/(\d+)\.html$',
        'author_pattern': r'/author_(\d+)\.html$',
        'translation_pattern': r'/gwfanyi_(\d+)\.html$',
        'appreciation_pattern': r'/gwshangxi_(\d+)\.html$',
    }
}
```

### 5. 数据库工具更新

更新了 `utils/db_utils.py`：
- 支持新的表结构清理
- 更新表记录统计功能

## 测试结果

### 成功测试的功能：
1. ✅ 数据库表创建和初始化
2. ✅ 朝代数据预置（12个朝代）
3. ✅ 诗词页面解析（http://shangshiwen.com/67827.html）
4. ✅ 数据保存到数据库
5. ✅ 关联关系建立

### 测试数据：
- **诗词**: 白头吟（ID: 67827）
- **作者**: 卓文君（ID: 785）
- **朝代**: 两汉
- **标签**: 13个（包括女子、爱情、唐诗三百首等）

## 数据库状态

当前数据库记录数：
- dynasties: 12 条记录
- authors: 1 条记录  
- tags: 13 条记录
- poems: 1 条记录
- poem_tags: 13 条记录
- translations: 0 条记录
- appreciations: 0 条记录

## 待完善功能

### 1. 翻译内容爬取
需要实现对翻译页面的解析：
- URL格式：`gwfanyi_4076.html`
- 提取翻译标题和内容

### 2. 赏析内容爬取
需要实现对赏析页面的解析：
- URL格式：`gwshangxi_5444.html`, `gwshangxi_5445.html`
- 提取赏析标题和内容

### 3. 作者详情页爬取
需要实现对作者页面的解析：
- URL格式：`author_785.html`
- 提取作者详细信息

### 4. 批量爬取功能
- 实现诗词列表页面的解析
- 支持分页爬取
- 添加爬取进度控制

## 使用方法

### 1. 测试单个诗词爬取：
```bash
python shangshiwen_spider.py
```

### 2. 运行主程序：
```bash
python main.py
```

### 3. 查看数据库状态：
```bash
python -c "from utils.db_utils import get_table_counts; print(get_table_counts())"
```

### 4. 清空数据库：
```bash
python -c "from utils.db_utils import clear_tables; clear_tables()"
```

## 技术特点

1. **模块化设计**: 爬虫、数据库、配置分离
2. **关系型数据库**: 充分利用外键关系
3. **数据完整性**: 避免重复数据，支持数据更新
4. **错误处理**: 完善的异常处理和日志记录
5. **可扩展性**: 易于添加新的解析功能

## 总结

项目已成功迁移到新网站，数据库结构更加合理，爬虫功能正常。基础框架已经建立，可以在此基础上继续完善翻译、赏析等功能的爬取。

"""
项目配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库配置
DATABASE_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': os.getenv('DB_NAME', 'gushiwen_spider'),
    'user': os.getenv('DB_USER', 'lee'),
    'password': os.getenv('DB_PASSWORD', ''),
}

# PostgreSQL连接URL
DATABASE_URL = f"postgresql://{DATABASE_CONFIG['user']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"

# 爬虫配置
SPIDER_CONFIG = {
    'user_agent': os.getenv('USER_AGENT', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
    'request_delay': float(os.getenv('REQUEST_DELAY', 1)),
    'concurrent_requests': int(os.getenv('CONCURRENT_REQUESTS', 8)),
}

# 反爬策略配置
ANTI_DETECTION_CONFIG = {
    # 代理配置
    'proxy': {
        'enabled': os.getenv('PROXY_ENABLED', 'false').lower() == 'true',
        # round_robin, random, best_performance
        'rotation_strategy': os.getenv('PROXY_STRATEGY', 'round_robin'),
        'check_url': os.getenv('PROXY_CHECK_URL', 'http://httpbin.org/ip'),
        # 5分钟
        'health_check_interval': int(os.getenv('PROXY_CHECK_INTERVAL', 300)),
        'max_failures': int(os.getenv('PROXY_MAX_FAILURES', 5)),
    },

    # 重试策略配置
    'retry': {
        'max_attempts': int(os.getenv('RETRY_MAX_ATTEMPTS', 3)),
        'base_delay': float(os.getenv('RETRY_BASE_DELAY', 1.0)),
        'max_delay': float(os.getenv('RETRY_MAX_DELAY', 60.0)),
        # fixed, linear, exponential, random
        'strategy': os.getenv('RETRY_STRATEGY', 'exponential'),
        'backoff_factor': float(os.getenv('RETRY_BACKOFF_FACTOR', 2.0)),
        'jitter': os.getenv('RETRY_JITTER', 'true').lower() == 'true',
    },

    # 请求频率控制
    'throttle': {
        'requests_per_second': float(os.getenv('THROTTLE_RPS', 1.0)),
        'requests_per_minute': int(os.getenv('THROTTLE_RPM', 60)),
        'requests_per_hour': int(os.getenv('THROTTLE_RPH', 3600)),
        'burst_size': int(os.getenv('THROTTLE_BURST', 5)),
        'adaptive': os.getenv('THROTTLE_ADAPTIVE', 'true').lower() == 'true',
        'min_delay': float(os.getenv('THROTTLE_MIN_DELAY', 0.5)),
        'max_delay': float(os.getenv('THROTTLE_MAX_DELAY', 10.0)),
    },

    # 请求头配置
    'headers': {
        'rotate_user_agent': os.getenv('ROTATE_USER_AGENT', 'true').lower() == 'true',
        'random_accept_language': os.getenv('RANDOM_ACCEPT_LANG', 'true').lower() == 'true',
        'add_sec_headers': os.getenv('ADD_SEC_HEADERS', 'true').lower() == 'true',
        'add_dnt_header': os.getenv('ADD_DNT_HEADER', 'false').lower() == 'true',
    },

    # 会话管理
    'session': {
        'keep_alive': os.getenv('SESSION_KEEP_ALIVE', 'true').lower() == 'true',
        'timeout': int(os.getenv('SESSION_TIMEOUT', 30)),
        'max_retries': int(os.getenv('SESSION_MAX_RETRIES', 3)),
        'pool_connections': int(os.getenv('SESSION_POOL_CONNECTIONS', 10)),
        'pool_maxsize': int(os.getenv('SESSION_POOL_MAXSIZE', 10)),
    },

    # 监控配置
    'monitoring': {
        'enabled': os.getenv('MONITORING_ENABLED', 'true').lower() == 'true',
        # 每100个请求记录一次统计
        'log_stats_interval': int(os.getenv('MONITORING_LOG_INTERVAL', 100)),
        # 失败率超过30%告警
        'alert_failure_rate': float(os.getenv('MONITORING_ALERT_FAILURE_RATE', 0.3)),
        # 响应时间超过5秒告警
        'alert_response_time': float(os.getenv('MONITORING_ALERT_RESPONSE_TIME', 5.0)),
    }
}

# 日志配置
LOG_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'file': os.getenv('LOG_FILE', 'logs/spider.log'),
}

# 目标网站配置
TARGET_SITES = {
    'shangshiwen': {
        'base_url': 'http://shangshiwen.com',
        'name': '尚诗文网',
        'poem_detail_pattern': r'/(\d+)\.html$',  # 诗词详情页面模式
        'author_pattern': r'/author_(\d+)\.html$',  # 作者页面模式
        'translation_pattern': r'/gwfanyi_(\d+)\.html$',  # 翻译页面模式
        'appreciation_pattern': r'/gwshangxi_(\d+)\.html$',  # 赏析页面模式
    }
}

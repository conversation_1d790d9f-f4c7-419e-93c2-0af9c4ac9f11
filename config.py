"""
项目配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库配置
DATABASE_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': os.getenv('DB_NAME', 'gushiwen_spider'),
    'user': os.getenv('DB_USER', 'lee'),
    'password': os.getenv('DB_PASSWORD', ''),
}

# PostgreSQL连接URL
DATABASE_URL = f"postgresql://{DATABASE_CONFIG['user']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"

# 爬虫配置
SPIDER_CONFIG = {
    'user_agent': os.getenv('USER_AGENT', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
    'request_delay': float(os.getenv('REQUEST_DELAY', 1)),
    'concurrent_requests': int(os.getenv('CONCURRENT_REQUESTS', 8)),
}

# 日志配置
LOG_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'file': os.getenv('LOG_FILE', 'logs/spider.log'),
}

# 目标网站配置
TARGET_SITES = {
    'shangshiwen': {
        'base_url': 'http://shangshiwen.com',
        'name': '尚诗文网',
        'poem_detail_pattern': r'/(\d+)\.html$',  # 诗词详情页面模式
        'author_pattern': r'/author_(\d+)\.html$',  # 作者页面模式
        'translation_pattern': r'/gwfanyi_(\d+)\.html$',  # 翻译页面模式
        'appreciation_pattern': r'/gwshangxi_(\d+)\.html$',  # 赏析页面模式
    }
}

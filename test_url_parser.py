#!/usr/bin/env python3
"""
URL解析工具测试脚本
"""
from utils.url_parser import <PERSON>gShiWenUrlParser, extract_tag_id, parse_url


def test_tag_id_extraction():
    """测试标签ID提取功能"""
    print("=" * 60)
    print("🏷️  测试标签ID提取功能")
    print("=" * 60)
    
    test_cases = [
        "tags_225_1_0_0.html",
        "tags_1_1_0_0.html", 
        "tags_45_1_0_0.html",
        "tags_473_1_0_0.html",
        "http://shangshiwen.com/tags_225_1_0_0.html",
        "/tags_225_1_0_0.html",
        "invalid_tag_url.html"
    ]
    
    print("使用类方法:")
    for url in test_cases:
        tag_id = ShangShiWenUrlParser.extract_tag_id(url)
        status = "✅" if tag_id else "❌"
        print(f"{status} {url:35s} -> {tag_id}")
    
    print("\n使用简化函数:")
    for url in test_cases:
        tag_id = extract_tag_id(url)
        status = "✅" if tag_id else "❌"
        print(f"{status} {url:35s} -> {tag_id}")


def test_all_url_types():
    """测试所有URL类型的解析"""
    print("\n" + "=" * 60)
    print("🔍 测试所有URL类型解析")
    print("=" * 60)
    
    test_urls = [
        # 诗词URL
        ("http://shangshiwen.com/67827.html", "poem", 67827),
        ("/67827.html", "poem", 67827),
        ("12345.html", "poem", 12345),
        
        # 作者URL
        ("/shiren/785_1.html", "author", 785),
        ("http://shangshiwen.com/shiren/1196_1.html", "author", 1196),
        
        # 标签URL
        ("tags_225_1_0_0.html", "tag", 225),
        ("tags_1_1_0_0.html", "tag", 1),
        ("tags_473_1_0_0.html", "tag", 473),
        
        # 翻译URL
        ("gwfanyi_4076.html", "translation", 4076),
        ("http://shangshiwen.com/gwfanyi_1234.html", "translation", 1234),
        
        # 赏析URL
        ("gwshangxi_5444.html", "appreciation", 5444),
        ("gwshangxi_5445.html", "appreciation", 5445),
        
        # 无效URL
        ("invalid_url.html", None, None),
        ("", None, None),
    ]
    
    print(f"{'URL':40s} {'期望类型':12s} {'期望ID':8s} {'实际类型':12s} {'实际ID':8s} {'状态':4s}")
    print("-" * 90)
    
    for url, expected_type, expected_id in test_urls:
        result = parse_url(url)
        actual_type = result['type']
        actual_id = result['id']
        
        # 检查结果是否正确
        type_ok = actual_type == expected_type
        id_ok = actual_id == expected_id
        status = "✅" if (type_ok and id_ok) else "❌"
        
        expected_type_str = expected_type or "None"
        expected_id_str = str(expected_id) if expected_id else "None"
        actual_type_str = actual_type or "None"
        actual_id_str = str(actual_id) if actual_id else "None"
        
        print(f"{url:40s} {expected_type_str:12s} {expected_id_str:8s} {actual_type_str:12s} {actual_id_str:8s} {status:4s}")


def test_url_validation():
    """测试URL验证功能"""
    print("\n" + "=" * 60)
    print("✅ 测试URL验证功能")
    print("=" * 60)
    
    validation_tests = [
        ("tags_225_1_0_0.html", "tag", True),
        ("tags_1_1_0_0.html", "tag", True),
        ("invalid_tag.html", "tag", False),
        ("/67827.html", "poem", True),
        ("/67827.html", "tag", False),
        ("/shiren/785_1.html", "author", True),
        ("gwfanyi_4076.html", "translation", True),
        ("gwshangxi_5444.html", "appreciation", True),
        ("invalid_url.html", None, False),
        ("tags_225_1_0_0.html", None, True),  # 任意有效类型
    ]
    
    print(f"{'URL':30s} {'类型':12s} {'期望':6s} {'实际':6s} {'状态':4s}")
    print("-" * 60)
    
    for url, url_type, expected in validation_tests:
        actual = ShangShiWenUrlParser.is_valid_url(url, url_type)
        status = "✅" if actual == expected else "❌"
        url_type_str = url_type or "any"
        
        print(f"{url:30s} {url_type_str:12s} {expected!s:6s} {actual!s:6s} {status:4s}")


def test_specific_tag_examples():
    """测试你提到的具体例子"""
    print("\n" + "=" * 60)
    print("🎯 测试具体例子")
    print("=" * 60)
    
    # 你提到的具体例子
    example_url = "tags_225_1_0_0.html"
    
    print(f"输入URL: {example_url}")
    
    # 方法1: 使用类方法
    tag_id_1 = ShangShiWenUrlParser.extract_tag_id(example_url)
    print(f"类方法提取: {tag_id_1}")
    
    # 方法2: 使用简化函数
    tag_id_2 = extract_tag_id(example_url)
    print(f"简化函数提取: {tag_id_2}")
    
    # 方法3: 使用智能解析
    result = parse_url(example_url)
    print(f"智能解析: {result}")
    
    # 验证结果
    expected_id = 225
    if tag_id_1 == tag_id_2 == result['id'] == expected_id:
        print(f"✅ 所有方法都正确提取到tag_id: {expected_id}")
    else:
        print(f"❌ 提取结果不一致或不正确")


def main():
    """主测试函数"""
    print("🚀 URL解析工具测试")
    
    # 测试标签ID提取
    test_tag_id_extraction()
    
    # 测试所有URL类型
    test_all_url_types()
    
    # 测试URL验证
    test_url_validation()
    
    # 测试具体例子
    test_specific_tag_examples()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)
    
    # 显示支持的URL类型
    supported_types = ShangShiWenUrlParser.get_supported_types()
    print(f"支持的URL类型: {', '.join(supported_types)}")


if __name__ == "__main__":
    main()

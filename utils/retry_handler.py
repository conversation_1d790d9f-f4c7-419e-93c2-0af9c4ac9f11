"""
智能重试处理器 - 提供多种重试策略和错误处理
"""
import time
import random
import functools
from typing import Callable, Any, Optional, List, Type
from loguru import logger
from dataclasses import dataclass
from enum import Enum


class RetryStrategy(Enum):
    """重试策略枚举"""
    FIXED = "fixed"  # 固定间隔
    LINEAR = "linear"  # 线性增长
    EXPONENTIAL = "exponential"  # 指数退避
    RANDOM = "random"  # 随机间隔


@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3  # 最大重试次数
    base_delay: float = 1.0  # 基础延迟时间(秒)
    max_delay: float = 60.0  # 最大延迟时间(秒)
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL  # 重试策略
    backoff_factor: float = 2.0  # 退避因子
    jitter: bool = True  # 是否添加随机抖动
    exceptions: tuple = (Exception,)  # 需要重试的异常类型


class CircuitBreaker:
    """熔断器 - 防止连续失败时继续请求"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold  # 失败阈值
        self.recovery_timeout = recovery_timeout  # 恢复超时时间
        self.failure_count = 0  # 失败计数
        self.last_failure_time = None  # 最后失败时间
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func: Callable, *args, **kwargs):
        """调用函数，带熔断保护"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                logger.info("熔断器进入半开状态")
            else:
                raise Exception("熔断器开启，拒绝请求")
        
        try:
            result = func(*args, **kwargs)
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
                logger.info("熔断器恢复正常")
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
                logger.warning(f"熔断器开启，连续失败 {self.failure_count} 次")
            
            raise e


class RetryHandler:
    """重试处理器"""
    
    def __init__(self, config: RetryConfig = None):
        self.config = config or RetryConfig()
        self.circuit_breaker = CircuitBreaker()
    
    def calculate_delay(self, attempt: int) -> float:
        """计算延迟时间"""
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = self.config.base_delay * attempt
        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (self.config.backoff_factor ** (attempt - 1))
        elif self.config.strategy == RetryStrategy.RANDOM:
            delay = random.uniform(self.config.base_delay, self.config.max_delay)
        else:
            delay = self.config.base_delay
        
        # 限制最大延迟
        delay = min(delay, self.config.max_delay)
        
        # 添加随机抖动
        if self.config.jitter:
            jitter_range = delay * 0.1  # 10% 抖动
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """判断是否应该重试"""
        if attempt >= self.config.max_attempts:
            return False
        
        # 检查异常类型
        if not isinstance(exception, self.config.exceptions):
            return False
        
        # 特殊状态码处理
        if hasattr(exception, 'response') and hasattr(exception.response, 'status_code'):
            status_code = exception.response.status_code
            
            # 4xx 客户端错误通常不需要重试
            if 400 <= status_code < 500 and status_code not in [429, 408]:
                logger.debug(f"状态码 {status_code} 不重试")
                return False
            
            # 5xx 服务器错误可以重试
            if 500 <= status_code < 600:
                return True
            
            # 429 Too Many Requests 需要重试
            if status_code == 429:
                return True
        
        return True
    
    def retry(self, func: Callable, *args, **kwargs) -> Any:
        """执行重试逻辑"""
        last_exception = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                # 使用熔断器保护
                result = self.circuit_breaker.call(func, *args, **kwargs)
                
                if attempt > 1:
                    logger.success(f"重试成功，第 {attempt} 次尝试")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                if not self.should_retry(e, attempt):
                    logger.error(f"不可重试的错误: {e}")
                    break
                
                if attempt < self.config.max_attempts:
                    delay = self.calculate_delay(attempt)
                    logger.warning(f"第 {attempt} 次尝试失败: {e}, {delay:.2f}秒后重试")
                    time.sleep(delay)
                else:
                    logger.error(f"重试失败，已达到最大尝试次数 {self.config.max_attempts}")
        
        # 所有重试都失败了
        raise last_exception


def retry_on_failure(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
    exceptions: tuple = (Exception,)
):
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = RetryConfig(
                max_attempts=max_attempts,
                base_delay=base_delay,
                strategy=strategy,
                exceptions=exceptions
            )
            handler = RetryHandler(config)
            return handler.retry(func, *args, **kwargs)
        return wrapper
    return decorator


class ErrorClassifier:
    """错误分类器 - 根据错误类型决定处理策略"""
    
    @staticmethod
    def classify_http_error(status_code: int) -> str:
        """分类HTTP错误"""
        if status_code == 429:
            return "rate_limit"
        elif status_code == 403:
            return "forbidden"
        elif status_code == 404:
            return "not_found"
        elif 500 <= status_code < 600:
            return "server_error"
        elif 400 <= status_code < 500:
            return "client_error"
        else:
            return "unknown"
    
    @staticmethod
    def get_retry_strategy(error_type: str) -> RetryConfig:
        """根据错误类型获取重试策略"""
        strategies = {
            "rate_limit": RetryConfig(
                max_attempts=5,
                base_delay=5.0,
                strategy=RetryStrategy.EXPONENTIAL,
                backoff_factor=2.0
            ),
            "server_error": RetryConfig(
                max_attempts=3,
                base_delay=2.0,
                strategy=RetryStrategy.EXPONENTIAL
            ),
            "network_error": RetryConfig(
                max_attempts=3,
                base_delay=1.0,
                strategy=RetryStrategy.LINEAR
            ),
            "forbidden": RetryConfig(
                max_attempts=1,  # 403 通常不需要重试
                base_delay=0.0
            )
        }
        
        return strategies.get(error_type, RetryConfig())


# 使用示例
if __name__ == "__main__":
    import requests
    
    # 示例1: 使用装饰器
    @retry_on_failure(max_attempts=3, base_delay=1.0)
    def fetch_url(url):
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        return response.text
    
    # 示例2: 直接使用重试处理器
    def test_retry():
        config = RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            strategy=RetryStrategy.EXPONENTIAL
        )
        handler = RetryHandler(config)
        
        def failing_function():
            if random.random() < 0.7:  # 70% 概率失败
                raise Exception("随机失败")
            return "成功"
        
        try:
            result = handler.retry(failing_function)
            print(f"结果: {result}")
        except Exception as e:
            print(f"最终失败: {e}")
    
    test_retry()

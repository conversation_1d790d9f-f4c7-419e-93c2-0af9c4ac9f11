"""
反检测爬虫基类 - 集成多种反爬策略
"""
import time
import random
import requests
from typing import Optional, Dict, Any
from fake_useragent import UserAgent
from loguru import logger

try:
    from .proxy_manager import ProxyManager, ProxyInfo
    from .retry_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, RetryConfig, RetryStrategy
    from .request_throttler import RequestThrottler
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(
        os.path.dirname(os.path.abspath(__file__))))
    from utils.proxy_manager import ProxyManager, ProxyInfo
    from utils.retry_handler import <PERSON><PERSON><PERSON>and<PERSON>, RetryConfig, RetryStrategy
    from utils.request_throttler import RequestThrottler


class AntiDetectionSpider:
    """反检测爬虫基类"""

    def __init__(self,
                 proxy_manager: Optional[ProxyManager] = None,
                 retry_config: Optional[RetryConfig] = None,
                 throttle_config: Optional[Dict] = None):

        # 初始化组件
        self.session = requests.Session()
        self.ua = UserAgent()
        self.proxy_manager = proxy_manager
        self.retry_handler = RetryHandler(retry_config or RetryConfig())
        self.throttler = RequestThrottler(**(throttle_config or {}))

        # 状态跟踪
        self.request_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.start_time = time.time()

        # 设置基础会话
        self.setup_session()

    def setup_session(self):
        """设置会话配置"""
        # 基础请求头
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        })

        # 设置超时
        self.session.timeout = 30

        # 禁用SSL验证警告（如果需要）
        requests.packages.urllib3.disable_warnings()

    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        headers = {}

        # 随机User-Agent
        headers['User-Agent'] = self.ua.random

        # 随机Accept-Language
        languages = [
            'zh-CN,zh;q=0.9,en;q=0.8',
            'zh-CN,zh;q=0.8,en;q=0.7',
            'en-US,en;q=0.9,zh;q=0.8',
            'zh-CN,zh;q=0.9',
        ]
        headers['Accept-Language'] = random.choice(languages)

        # 随机DNT
        if random.random() < 0.3:
            headers['DNT'] = '1'

        # 随机Sec-CH-UA (Chrome特有)
        if 'Chrome' in headers['User-Agent']:
            headers['Sec-CH-UA'] = '"Google Chrome";v="120", "Chromium";v="120", "Not_A Brand";v="99"'
            headers['Sec-CH-UA-Mobile'] = '?0'
            headers['Sec-CH-UA-Platform'] = f'"{random.choice(["Windows", "macOS", "Linux"])}"'

        return headers

    def get_proxy_config(self) -> Optional[Dict[str, str]]:
        """获取代理配置"""
        if not self.proxy_manager:
            return None

        proxy = self.proxy_manager.get_proxy()
        if not proxy:
            return None

        return {
            'http': proxy.url,
            'https': proxy.url
        }

    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """发起请求（带反检测功能）"""
        def _request():
            # 请求频率控制
            self.throttler.wait_if_needed()

            # 更新请求头
            headers = kwargs.pop('headers', {})
            headers.update(self.get_random_headers())

            # 设置代理
            proxies = self.get_proxy_config()
            if proxies:
                kwargs['proxies'] = proxies

            # 记录请求
            self.request_count += 1
            start_time = time.time()

            try:
                # 发起请求
                response = self.session.request(
                    method, url, headers=headers, **kwargs)
                response.raise_for_status()

                # 记录成功
                response_time = time.time() - start_time
                self.success_count += 1

                # 更新代理统计
                if proxies and self.proxy_manager:
                    proxy = self.proxy_manager.get_proxy()
                    if proxy:
                        self.proxy_manager.mark_success(proxy, response_time)

                # 更新限流器
                self.throttler.record_request(response_time)

                logger.debug(f"请求成功: {url} ({response_time:.2f}s)")
                return response

            except Exception as e:
                # 记录失败
                self.failure_count += 1

                # 更新代理统计
                if proxies and self.proxy_manager:
                    proxy = self.proxy_manager.get_proxy()
                    if proxy:
                        self.proxy_manager.mark_failure(proxy)

                logger.warning(f"请求失败: {url} - {e}")
                raise e

        # 使用重试机制
        try:
            return self.retry_handler.retry(_request)
        except Exception as e:
            logger.error(f"重试后仍然失败: {url} - {e}")
            return None

    def get(self, url: str, **kwargs) -> Optional[requests.Response]:
        """GET请求"""
        return self.make_request(url, 'GET', **kwargs)

    def post(self, url: str, **kwargs) -> Optional[requests.Response]:
        """POST请求"""
        return self.make_request(url, 'POST', **kwargs)

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        runtime = time.time() - self.start_time
        success_rate = self.success_count / \
            self.request_count if self.request_count > 0 else 0

        stats = {
            'runtime': runtime,
            'total_requests': self.request_count,
            'successful_requests': self.success_count,
            'failed_requests': self.failure_count,
            'success_rate': success_rate,
            'requests_per_minute': (self.request_count / runtime) * 60 if runtime > 0 else 0
        }

        # 添加代理统计
        if self.proxy_manager:
            stats['proxy_stats'] = self.proxy_manager.get_stats()

        # 添加限流统计
        stats['throttle_stats'] = self.throttler.get_stats()

        return stats

    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()

        logger.info("=== 爬虫统计信息 ===")
        logger.info(f"运行时间: {stats['runtime']:.2f}秒")
        logger.info(f"总请求数: {stats['total_requests']}")
        logger.info(f"成功请求: {stats['successful_requests']}")
        logger.info(f"失败请求: {stats['failed_requests']}")
        logger.info(f"成功率: {stats['success_rate']:.2%}")
        logger.info(f"请求频率: {stats['requests_per_minute']:.2f}/分钟")

        if 'proxy_stats' in stats:
            proxy_stats = stats['proxy_stats']
            logger.info(
                f"代理统计: {proxy_stats['active_proxies']}/{proxy_stats['total_proxies']} 可用")

    def adaptive_delay(self, base_delay: float = 1.0) -> float:
        """自适应延迟 - 根据成功率动态调整"""
        if self.request_count < 10:
            return base_delay

        success_rate = self.success_count / self.request_count

        if success_rate > 0.9:
            # 成功率高，可以加快速度
            return base_delay * 0.8
        elif success_rate > 0.7:
            # 成功率正常
            return base_delay
        elif success_rate > 0.5:
            # 成功率偏低，放慢速度
            return base_delay * 1.5
        else:
            # 成功率很低，大幅放慢
            return base_delay * 3.0

    def should_continue(self) -> bool:
        """判断是否应该继续爬取"""
        # 如果连续失败太多次，暂停爬取
        if self.request_count >= 20:
            recent_success_rate = self.success_count / self.request_count
            if recent_success_rate < 0.3:
                logger.warning("成功率过低，建议暂停爬取")
                return False

        return True

    def cleanup(self):
        """清理资源"""
        if self.session:
            self.session.close()

        # 打印最终统计
        self.print_stats()


# 使用示例
if __name__ == "__main__":
    from proxy_manager import ProxyManager

    # 创建代理管理器
    proxy_manager = ProxyManager()

    # 创建爬虫
    spider = AntiDetectionSpider(proxy_manager=proxy_manager)

    try:
        # 测试请求
        response = spider.get("http://httpbin.org/ip")
        if response:
            print(f"响应: {response.json()}")

        # 显示统计
        spider.print_stats()

    finally:
        spider.cleanup()

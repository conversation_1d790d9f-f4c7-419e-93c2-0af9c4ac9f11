"""
浏览器自动化爬虫 - 使用Selenium/Playwright处理JavaScript渲染的页面
"""
import time
import random
from typing import Optional, Dict, List
from loguru import logger
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import TimeoutException, WebDriverException


class BrowserSpider:
    """浏览器自动化爬虫"""
    
    def __init__(self, browser='chrome', headless=True, proxy=None):
        self.browser = browser.lower()
        self.headless = headless
        self.proxy = proxy
        self.driver = None
        self.wait = None
        
        self._setup_driver()
    
    def _setup_driver(self):
        """设置浏览器驱动"""
        try:
            if self.browser == 'chrome':
                self.driver = self._setup_chrome()
            elif self.browser == 'firefox':
                self.driver = self._setup_firefox()
            else:
                raise ValueError(f"不支持的浏览器: {self.browser}")
            
            # 设置等待器
            self.wait = WebDriverWait(self.driver, 10)
            
            # 设置窗口大小
            self.driver.set_window_size(1920, 1080)
            
            logger.info(f"浏览器驱动初始化完成: {self.browser}")
            
        except Exception as e:
            logger.error(f"浏览器驱动初始化失败: {e}")
            raise e
    
    def _setup_chrome(self):
        """设置Chrome驱动"""
        options = ChromeOptions()
        
        # 基础设置
        if self.headless:
            options.add_argument('--headless')
        
        # 反检测设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 性能优化
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')  # 如果不需要JS可以禁用
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-extensions')
        
        # 代理设置
        if self.proxy:
            options.add_argument(f'--proxy-server={self.proxy}')
        
        # 随机User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        options.add_argument(f'--user-agent={random.choice(user_agents)}')
        
        driver = webdriver.Chrome(options=options)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    
    def _setup_firefox(self):
        """设置Firefox驱动"""
        options = FirefoxOptions()
        
        if self.headless:
            options.add_argument('--headless')
        
        # 代理设置
        if self.proxy:
            proxy_parts = self.proxy.split(':')
            if len(proxy_parts) == 2:
                options.set_preference('network.proxy.type', 1)
                options.set_preference('network.proxy.http', proxy_parts[0])
                options.set_preference('network.proxy.http_port', int(proxy_parts[1]))
                options.set_preference('network.proxy.ssl', proxy_parts[0])
                options.set_preference('network.proxy.ssl_port', int(proxy_parts[1]))
        
        return webdriver.Firefox(options=options)
    
    def get_page(self, url: str, wait_element: str = None, timeout: int = 10) -> bool:
        """获取页面"""
        try:
            logger.info(f"访问页面: {url}")
            
            # 随机延迟
            time.sleep(random.uniform(1, 3))
            
            self.driver.get(url)
            
            # 等待特定元素加载
            if wait_element:
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, wait_element)))
            else:
                # 等待页面加载完成
                self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
            
            logger.success(f"页面加载完成: {url}")
            return True
            
        except TimeoutException:
            logger.error(f"页面加载超时: {url}")
            return False
        except WebDriverException as e:
            logger.error(f"浏览器异常: {e}")
            return False
    
    def find_element(self, selector: str, by: str = 'css') -> Optional[object]:
        """查找元素"""
        try:
            if by == 'css':
                return self.driver.find_element(By.CSS_SELECTOR, selector)
            elif by == 'xpath':
                return self.driver.find_element(By.XPATH, selector)
            elif by == 'id':
                return self.driver.find_element(By.ID, selector)
            elif by == 'class':
                return self.driver.find_element(By.CLASS_NAME, selector)
            else:
                raise ValueError(f"不支持的选择器类型: {by}")
        except Exception as e:
            logger.debug(f"元素未找到: {selector} - {e}")
            return None
    
    def find_elements(self, selector: str, by: str = 'css') -> List[object]:
        """查找多个元素"""
        try:
            if by == 'css':
                return self.driver.find_elements(By.CSS_SELECTOR, selector)
            elif by == 'xpath':
                return self.driver.find_elements(By.XPATH, selector)
            elif by == 'id':
                return self.driver.find_elements(By.ID, selector)
            elif by == 'class':
                return self.driver.find_elements(By.CLASS_NAME, selector)
            else:
                raise ValueError(f"不支持的选择器类型: {by}")
        except Exception as e:
            logger.debug(f"元素未找到: {selector} - {e}")
            return []
    
    def get_text(self, selector: str, by: str = 'css') -> Optional[str]:
        """获取元素文本"""
        element = self.find_element(selector, by)
        return element.text if element else None
    
    def get_attribute(self, selector: str, attribute: str, by: str = 'css') -> Optional[str]:
        """获取元素属性"""
        element = self.find_element(selector, by)
        return element.get_attribute(attribute) if element else None
    
    def click_element(self, selector: str, by: str = 'css') -> bool:
        """点击元素"""
        try:
            element = self.find_element(selector, by)
            if element:
                # 滚动到元素位置
                self.driver.execute_script("arguments[0].scrollIntoView();", element)
                time.sleep(0.5)
                
                # 点击元素
                element.click()
                return True
            return False
        except Exception as e:
            logger.error(f"点击元素失败: {selector} - {e}")
            return False
    
    def input_text(self, selector: str, text: str, by: str = 'css') -> bool:
        """输入文本"""
        try:
            element = self.find_element(selector, by)
            if element:
                element.clear()
                
                # 模拟人类输入
                for char in text:
                    element.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.15))
                
                return True
            return False
        except Exception as e:
            logger.error(f"输入文本失败: {selector} - {e}")
            return False
    
    def scroll_page(self, direction: str = 'down', pixels: int = 500):
        """滚动页面"""
        if direction == 'down':
            self.driver.execute_script(f"window.scrollBy(0, {pixels});")
        elif direction == 'up':
            self.driver.execute_script(f"window.scrollBy(0, -{pixels});")
        elif direction == 'top':
            self.driver.execute_script("window.scrollTo(0, 0);")
        elif direction == 'bottom':
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
    
    def wait_for_element(self, selector: str, timeout: int = 10, by: str = 'css') -> bool:
        """等待元素出现"""
        try:
            if by == 'css':
                WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
            elif by == 'xpath':
                WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
            return True
        except TimeoutException:
            return False
    
    def execute_script(self, script: str, *args):
        """执行JavaScript"""
        return self.driver.execute_script(script, *args)
    
    def get_page_source(self) -> str:
        """获取页面源码"""
        return self.driver.page_source
    
    def get_current_url(self) -> str:
        """获取当前URL"""
        return self.driver.current_url
    
    def take_screenshot(self, filename: str = None) -> bool:
        """截图"""
        try:
            if not filename:
                filename = f"screenshot_{int(time.time())}.png"
            
            return self.driver.save_screenshot(filename)
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def handle_alert(self, accept: bool = True) -> bool:
        """处理弹窗"""
        try:
            alert = self.driver.switch_to.alert
            if accept:
                alert.accept()
            else:
                alert.dismiss()
            return True
        except Exception as e:
            logger.debug(f"没有弹窗或处理失败: {e}")
            return False
    
    def switch_to_frame(self, frame_selector: str) -> bool:
        """切换到iframe"""
        try:
            frame = self.find_element(frame_selector)
            if frame:
                self.driver.switch_to.frame(frame)
                return True
            return False
        except Exception as e:
            logger.error(f"切换iframe失败: {e}")
            return False
    
    def switch_to_default_content(self):
        """切换回主页面"""
        self.driver.switch_to.default_content()
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")


# 使用示例
if __name__ == "__main__":
    spider = BrowserSpider(browser='chrome', headless=True)
    
    try:
        # 访问页面
        if spider.get_page("http://shangshiwen.com/67827.html"):
            # 获取标题
            title = spider.get_text("h1")
            print(f"标题: {title}")
            
            # 获取内容
            content = spider.get_text(".poem-content")
            print(f"内容: {content}")
            
            # 截图
            spider.take_screenshot("test.png")
    
    finally:
        spider.close()

"""
代理管理器 - 提供代理轮换和健康检查功能
"""
import random
import time
import requests
from typing import List, Dict, Optional
from loguru import logger
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed


@dataclass
class ProxyInfo:
    """代理信息"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = 'http'  # http, https, socks5
    is_active: bool = True
    success_count: int = 0
    fail_count: int = 0
    last_used: Optional[float] = None
    response_time: float = 0.0

    @property
    def url(self) -> str:
        """获取代理URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"

    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.fail_count
        return self.success_count / total if total > 0 else 0.0


class ProxyManager:
    """代理管理器"""
    
    def __init__(self, proxies: List[Dict] = None, check_url: str = "http://httpbin.org/ip"):
        self.proxies: List[ProxyInfo] = []
        self.check_url = check_url
        self.current_index = 0
        
        if proxies:
            self.load_proxies(proxies)
    
    def load_proxies(self, proxies: List[Dict]):
        """加载代理列表"""
        for proxy_data in proxies:
            proxy = ProxyInfo(**proxy_data)
            self.proxies.append(proxy)
        logger.info(f"加载了 {len(self.proxies)} 个代理")
    
    def add_proxy(self, host: str, port: int, **kwargs):
        """添加单个代理"""
        proxy = ProxyInfo(host=host, port=port, **kwargs)
        self.proxies.append(proxy)
        logger.info(f"添加代理: {proxy.url}")
    
    def get_proxy(self, strategy: str = "round_robin") -> Optional[ProxyInfo]:
        """获取可用代理"""
        active_proxies = [p for p in self.proxies if p.is_active]
        
        if not active_proxies:
            logger.warning("没有可用的代理")
            return None
        
        if strategy == "round_robin":
            proxy = active_proxies[self.current_index % len(active_proxies)]
            self.current_index += 1
        elif strategy == "random":
            proxy = random.choice(active_proxies)
        elif strategy == "best_performance":
            proxy = min(active_proxies, key=lambda p: p.response_time)
        elif strategy == "highest_success_rate":
            proxy = max(active_proxies, key=lambda p: p.success_rate)
        else:
            proxy = active_proxies[0]
        
        proxy.last_used = time.time()
        return proxy
    
    def mark_success(self, proxy: ProxyInfo, response_time: float = 0.0):
        """标记代理成功"""
        proxy.success_count += 1
        proxy.response_time = response_time
        if not proxy.is_active:
            proxy.is_active = True
            logger.info(f"代理恢复活跃: {proxy.url}")
    
    def mark_failure(self, proxy: ProxyInfo):
        """标记代理失败"""
        proxy.fail_count += 1
        
        # 如果失败率过高，暂时禁用
        if proxy.fail_count >= 5 and proxy.success_rate < 0.3:
            proxy.is_active = False
            logger.warning(f"代理被禁用: {proxy.url} (成功率: {proxy.success_rate:.2%})")
    
    def check_proxy(self, proxy: ProxyInfo, timeout: int = 10) -> bool:
        """检查单个代理是否可用"""
        try:
            start_time = time.time()
            proxies = {
                'http': proxy.url,
                'https': proxy.url
            }
            
            response = requests.get(
                self.check_url,
                proxies=proxies,
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.mark_success(proxy, response_time)
                return True
            else:
                self.mark_failure(proxy)
                return False
                
        except Exception as e:
            logger.debug(f"代理检查失败 {proxy.url}: {e}")
            self.mark_failure(proxy)
            return False
    
    def check_all_proxies(self, max_workers: int = 10) -> Dict[str, int]:
        """并发检查所有代理"""
        logger.info("开始检查所有代理...")
        
        results = {"active": 0, "inactive": 0}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_proxy = {
                executor.submit(self.check_proxy, proxy): proxy 
                for proxy in self.proxies
            }
            
            for future in as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    is_active = future.result()
                    if is_active:
                        results["active"] += 1
                    else:
                        results["inactive"] += 1
                except Exception as e:
                    logger.error(f"检查代理时出错 {proxy.url}: {e}")
                    results["inactive"] += 1
        
        logger.info(f"代理检查完成: {results['active']} 个可用, {results['inactive']} 个不可用")
        return results
    
    def get_stats(self) -> Dict:
        """获取代理统计信息"""
        active_count = sum(1 for p in self.proxies if p.is_active)
        total_count = len(self.proxies)
        
        if active_count > 0:
            avg_response_time = sum(p.response_time for p in self.proxies if p.is_active) / active_count
            avg_success_rate = sum(p.success_rate for p in self.proxies if p.is_active) / active_count
        else:
            avg_response_time = 0
            avg_success_rate = 0
        
        return {
            "total_proxies": total_count,
            "active_proxies": active_count,
            "inactive_proxies": total_count - active_count,
            "avg_response_time": avg_response_time,
            "avg_success_rate": avg_success_rate
        }
    
    def remove_inactive_proxies(self):
        """移除不活跃的代理"""
        before_count = len(self.proxies)
        self.proxies = [p for p in self.proxies if p.is_active]
        removed_count = before_count - len(self.proxies)
        
        if removed_count > 0:
            logger.info(f"移除了 {removed_count} 个不活跃的代理")


# 免费代理获取器
class FreeProxyFetcher:
    """免费代理获取器"""
    
    @staticmethod
    def fetch_from_proxylist() -> List[Dict]:
        """从 free-proxy-list.net 获取免费代理"""
        try:
            url = "https://www.proxy-list.download/api/v1/get?type=http"
            response = requests.get(url, timeout=10)
            
            proxies = []
            for line in response.text.strip().split('\n'):
                if ':' in line:
                    host, port = line.strip().split(':')
                    proxies.append({
                        'host': host,
                        'port': int(port),
                        'protocol': 'http'
                    })
            
            logger.info(f"从 proxy-list.download 获取到 {len(proxies)} 个代理")
            return proxies
            
        except Exception as e:
            logger.error(f"获取免费代理失败: {e}")
            return []
    
    @staticmethod
    def get_sample_proxies() -> List[Dict]:
        """获取示例代理配置"""
        return [
            {'host': '127.0.0.1', 'port': 8080, 'protocol': 'http'},
            {'host': '127.0.0.1', 'port': 8081, 'protocol': 'http'},
            # 添加更多代理...
        ]


if __name__ == "__main__":
    # 测试代码
    manager = ProxyManager()
    
    # 添加示例代理
    sample_proxies = FreeProxyFetcher.get_sample_proxies()
    manager.load_proxies(sample_proxies)
    
    # 检查代理
    manager.check_all_proxies()
    
    # 显示统计
    stats = manager.get_stats()
    print(f"代理统计: {stats}")

"""
请求频率控制器 - 智能控制请求频率，避免被限流
"""
import time
import random
import threading
from typing import List, Optional
from collections import deque
from dataclasses import dataclass
from loguru import logger


@dataclass
class ThrottleConfig:
    """限流配置"""
    requests_per_second: float = 1.0  # 每秒请求数
    requests_per_minute: int = 60  # 每分钟请求数
    requests_per_hour: int = 3600  # 每小时请求数
    burst_size: int = 5  # 突发请求数量
    adaptive: bool = True  # 是否启用自适应调整
    min_delay: float = 0.5  # 最小延迟
    max_delay: float = 10.0  # 最大延迟


class RequestThrottler:
    """请求频率控制器"""
    
    def __init__(self, config: ThrottleConfig = None):
        self.config = config or ThrottleConfig()
        self.lock = threading.Lock()
        
        # 请求时间记录
        self.request_times = deque()  # 所有请求时间
        self.recent_requests = deque()  # 最近的请求（用于突发控制）
        
        # 统计信息
        self.total_requests = 0
        self.total_wait_time = 0.0
        self.adaptive_delay = 1.0 / self.config.requests_per_second
        
        # 响应时间统计（用于自适应调整）
        self.response_times = deque(maxlen=100)
        self.error_count = 0
        self.last_error_time = None
    
    def _cleanup_old_records(self):
        """清理过期的请求记录"""
        current_time = time.time()
        
        # 清理1小时前的记录
        while self.request_times and current_time - self.request_times[0] > 3600:
            self.request_times.popleft()
        
        # 清理突发请求记录（保留最近10秒）
        while self.recent_requests and current_time - self.recent_requests[0] > 10:
            self.recent_requests.popleft()
    
    def _get_requests_in_window(self, window_seconds: int) -> int:
        """获取指定时间窗口内的请求数"""
        current_time = time.time()
        cutoff_time = current_time - window_seconds
        
        count = 0
        for request_time in reversed(self.request_times):
            if request_time >= cutoff_time:
                count += 1
            else:
                break
        
        return count
    
    def _calculate_delay(self) -> float:
        """计算需要等待的时间"""
        current_time = time.time()
        
        # 基础延迟
        base_delay = 1.0 / self.config.requests_per_second
        
        # 检查各种限制
        delays = [0.0]
        
        # 1. 每秒限制
        if len(self.recent_requests) > 0:
            last_request_time = self.recent_requests[-1]
            time_since_last = current_time - last_request_time
            if time_since_last < base_delay:
                delays.append(base_delay - time_since_last)
        
        # 2. 每分钟限制
        requests_per_minute = self._get_requests_in_window(60)
        if requests_per_minute >= self.config.requests_per_minute:
            # 计算到下一分钟的等待时间
            oldest_in_minute = current_time - 60
            for request_time in self.request_times:
                if request_time > oldest_in_minute:
                    delays.append(request_time + 60 - current_time)
                    break
        
        # 3. 每小时限制
        requests_per_hour = self._get_requests_in_window(3600)
        if requests_per_hour >= self.config.requests_per_hour:
            # 计算到下一小时的等待时间
            oldest_in_hour = current_time - 3600
            for request_time in self.request_times:
                if request_time > oldest_in_hour:
                    delays.append(request_time + 3600 - current_time)
                    break
        
        # 4. 突发限制
        if len(self.recent_requests) >= self.config.burst_size:
            # 如果突发请求过多，增加延迟
            delays.append(2.0)
        
        # 5. 自适应延迟
        if self.config.adaptive:
            delays.append(self.adaptive_delay)
        
        # 6. 错误恢复延迟
        if self.last_error_time and current_time - self.last_error_time < 60:
            # 如果最近有错误，增加延迟
            error_delay = min(5.0, 2.0 ** (self.error_count - 1))
            delays.append(error_delay)
        
        # 取最大延迟
        delay = max(delays)
        
        # 限制在配置范围内
        delay = max(self.config.min_delay, min(delay, self.config.max_delay))
        
        # 添加随机抖动（±10%）
        jitter = delay * 0.1 * (random.random() * 2 - 1)
        delay += jitter
        
        return max(0, delay)
    
    def wait_if_needed(self):
        """如果需要，等待一段时间"""
        with self.lock:
            self._cleanup_old_records()
            
            delay = self._calculate_delay()
            
            if delay > 0:
                logger.debug(f"限流等待 {delay:.2f} 秒")
                self.total_wait_time += delay
                time.sleep(delay)
            
            # 记录请求时间
            current_time = time.time()
            self.request_times.append(current_time)
            self.recent_requests.append(current_time)
            self.total_requests += 1
    
    def record_request(self, response_time: float, success: bool = True):
        """记录请求结果，用于自适应调整"""
        with self.lock:
            if success:
                self.response_times.append(response_time)
                self.error_count = max(0, self.error_count - 1)  # 成功时减少错误计数
                
                # 自适应调整
                if self.config.adaptive and len(self.response_times) >= 10:
                    avg_response_time = sum(self.response_times) / len(self.response_times)
                    
                    # 如果响应时间很快，可以稍微加快请求
                    if avg_response_time < 0.5:
                        self.adaptive_delay *= 0.95
                    # 如果响应时间较慢，放慢请求
                    elif avg_response_time > 2.0:
                        self.adaptive_delay *= 1.05
                    
                    # 限制自适应延迟范围
                    min_adaptive = 1.0 / (self.config.requests_per_second * 2)
                    max_adaptive = 1.0 / (self.config.requests_per_second * 0.5)
                    self.adaptive_delay = max(min_adaptive, min(self.adaptive_delay, max_adaptive))
            
            else:
                self.error_count += 1
                self.last_error_time = time.time()
                
                # 错误时增加自适应延迟
                if self.config.adaptive:
                    self.adaptive_delay *= 1.2
    
    def record_error(self, error_type: str = "unknown"):
        """记录错误"""
        self.record_request(0, success=False)
        logger.warning(f"记录错误: {error_type}")
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        with self.lock:
            current_time = time.time()
            
            # 计算各时间窗口的请求数
            requests_last_minute = self._get_requests_in_window(60)
            requests_last_hour = self._get_requests_in_window(3600)
            
            # 计算平均响应时间
            avg_response_time = 0
            if self.response_times:
                avg_response_time = sum(self.response_times) / len(self.response_times)
            
            return {
                'total_requests': self.total_requests,
                'total_wait_time': self.total_wait_time,
                'requests_last_minute': requests_last_minute,
                'requests_last_hour': requests_last_hour,
                'avg_response_time': avg_response_time,
                'current_adaptive_delay': self.adaptive_delay,
                'error_count': self.error_count,
                'avg_wait_per_request': self.total_wait_time / self.total_requests if self.total_requests > 0 else 0
            }
    
    def reset_stats(self):
        """重置统计信息"""
        with self.lock:
            self.request_times.clear()
            self.recent_requests.clear()
            self.response_times.clear()
            self.total_requests = 0
            self.total_wait_time = 0.0
            self.error_count = 0
            self.last_error_time = None
            self.adaptive_delay = 1.0 / self.config.requests_per_second
    
    def update_config(self, **kwargs):
        """更新配置"""
        with self.lock:
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                    logger.info(f"更新限流配置: {key} = {value}")
    
    def get_current_rate(self) -> float:
        """获取当前请求频率（每秒）"""
        requests_last_minute = self._get_requests_in_window(60)
        return requests_last_minute / 60.0
    
    def is_rate_limited(self) -> bool:
        """检查是否被限流"""
        return self.error_count > 3 or self.adaptive_delay > 5.0


# 全局限流器实例
_global_throttler = None


def get_global_throttler(config: ThrottleConfig = None) -> RequestThrottler:
    """获取全局限流器实例"""
    global _global_throttler
    if _global_throttler is None:
        _global_throttler = RequestThrottler(config)
    return _global_throttler


def throttle_request(func):
    """限流装饰器"""
    def wrapper(*args, **kwargs):
        throttler = get_global_throttler()
        throttler.wait_if_needed()
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            response_time = time.time() - start_time
            throttler.record_request(response_time, success=True)
            return result
        except Exception as e:
            throttler.record_request(0, success=False)
            raise e
    
    return wrapper


# 使用示例
if __name__ == "__main__":
    import requests
    
    # 创建限流器
    config = ThrottleConfig(
        requests_per_second=2.0,
        requests_per_minute=100,
        adaptive=True
    )
    throttler = RequestThrottler(config)
    
    # 模拟请求
    for i in range(10):
        throttler.wait_if_needed()
        
        start_time = time.time()
        try:
            # 模拟请求
            time.sleep(random.uniform(0.1, 0.5))
            response_time = time.time() - start_time
            throttler.record_request(response_time, success=True)
            print(f"请求 {i+1} 完成，响应时间: {response_time:.2f}s")
        except Exception as e:
            throttler.record_error()
            print(f"请求 {i+1} 失败: {e}")
    
    # 显示统计
    stats = throttler.get_stats()
    print(f"\n统计信息: {stats}")

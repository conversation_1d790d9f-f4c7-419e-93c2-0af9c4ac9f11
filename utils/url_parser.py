"""
URL解析工具类 - 用于从shangshiwen.com的URL中提取各种ID
"""
import re
from typing import Optional, Dict, Any
from loguru import logger


class ShangShiWenUrlParser:
    """尚诗文网URL解析器"""

    # URL模式定义
    PATTERNS = {
        'poem': r'/(\d+)\.html$',                    # 诗词详情页: /67827.html
        'author': r'/shiren/(\d+)_1\.html$',         # 作者页面: /shiren/785_1.html
        'tag': r'tags_(\d+)_1_0_0\.html$',          # 标签页面: tags_225_1_0_0.html
        'translation': r'gwfanyi_(\d+)\.html$',      # 翻译页面: gwfanyi_4076.html
        # 赏析页面: gwshangxi_5444.html
        'appreciation': r'gwshangxi_(\d+)\.html$',
    }

    @classmethod
    def extract_poem_id(cls, url: str) -> Optional[int]:
        """
        从诗词URL中提取诗词ID

        Args:
            url: 诗词URL，如 "http://shangshiwen.com/67827.html" 或 "/67827.html"

        Returns:
            诗词ID，如 67827，如果解析失败返回 None

        Examples:
            >>> ShangShiWenUrlParser.extract_poem_id("http://shangshiwen.com/67827.html")
            67827
            >>> ShangShiWenUrlParser.extract_poem_id("/67827.html")
            67827
        """
        match = re.search(cls.PATTERNS['poem'], url)
        if match:
            poem_id = int(match.group(1))
            logger.debug(f"提取诗词ID: {url} -> {poem_id}")
            return poem_id
        logger.warning(f"无法从URL提取诗词ID: {url}")
        return None

    @classmethod
    def extract_author_id(cls, url: str) -> Optional[int]:
        """
        从作者URL中提取作者ID

        Args:
            url: 作者URL，如 "/shiren/785_1.html"

        Returns:
            作者ID，如 785，如果解析失败返回 None

        Examples:
            >>> ShangShiWenUrlParser.extract_author_id("/shiren/785_1.html")
            785
        """
        match = re.search(cls.PATTERNS['author'], url)
        if match:
            author_id = int(match.group(1))
            logger.debug(f"提取作者ID: {url} -> {author_id}")
            return author_id
        logger.warning(f"无法从URL提取作者ID: {url}")
        return None

    @classmethod
    def extract_tag_id(cls, url: str) -> Optional[int]:
        """
        从标签URL中提取标签ID

        Args:
            url: 标签URL，如 "tags_225_1_0_0.html"

        Returns:
            标签ID，如 225，如果解析失败返回 None

        Examples:
            >>> ShangShiWenUrlParser.extract_tag_id("tags_225_1_0_0.html")
            225
            >>> ShangShiWenUrlParser.extract_tag_id("tags_1_1_0_0.html")
            1
        """
        match = re.search(cls.PATTERNS['tag'], url)
        if match:
            tag_id = int(match.group(1))
            logger.debug(f"提取标签ID: {url} -> {tag_id}")
            return tag_id
        logger.warning(f"无法从URL提取标签ID: {url}")
        return None

    @classmethod
    def extract_translation_id(cls, url: str) -> Optional[int]:
        """
        从翻译URL中提取翻译ID

        Args:
            url: 翻译URL，如 "gwfanyi_4076.html"

        Returns:
            翻译ID，如 4076，如果解析失败返回 None

        Examples:
            >>> ShangShiWenUrlParser.extract_translation_id("gwfanyi_4076.html")
            4076
        """
        match = re.search(cls.PATTERNS['translation'], url)
        if match:
            translation_id = int(match.group(1))
            logger.debug(f"提取翻译ID: {url} -> {translation_id}")
            return translation_id
        logger.warning(f"无法从URL提取翻译ID: {url}")
        return None

    @classmethod
    def extract_appreciation_id(cls, url: str) -> Optional[int]:
        """
        从赏析URL中提取赏析ID

        Args:
            url: 赏析URL，如 "gwshangxi_5444.html"

        Returns:
            赏析ID，如 5444，如果解析失败返回 None

        Examples:
            >>> ShangShiWenUrlParser.extract_appreciation_id("gwshangxi_5444.html")
            5444
        """
        match = re.search(cls.PATTERNS['appreciation'], url)
        if match:
            appreciation_id = int(match.group(1))
            logger.debug(f"提取赏析ID: {url} -> {appreciation_id}")
            return appreciation_id
        logger.warning(f"无法从URL提取赏析ID: {url}")
        return None

    @classmethod
    def parse_url(cls, url: str) -> Dict[str, Any]:
        """
        智能解析URL，自动识别类型并提取ID

        Args:
            url: 任意shangshiwen.com的URL

        Returns:
            解析结果字典，包含 type 和 id 字段

        Examples:
            >>> ShangShiWenUrlParser.parse_url("http://shangshiwen.com/67827.html")
            {'type': 'poem', 'id': 67827, 'url': 'http://shangshiwen.com/67827.html'}

            >>> ShangShiWenUrlParser.parse_url("tags_225_1_0_0.html")
            {'type': 'tag', 'id': 225, 'url': 'tags_225_1_0_0.html'}
        """
        result = {
            'type': None,
            'id': None,
            'url': url
        }

        # 按优先级尝试匹配不同类型的URL
        for url_type, pattern in cls.PATTERNS.items():
            match = re.search(pattern, url)
            if match:
                result['type'] = url_type
                result['id'] = int(match.group(1))
                logger.debug(f"URL解析成功: {url} -> {url_type}:{result['id']}")
                return result

        logger.warning(f"无法识别URL类型: {url}")
        return result

    @classmethod
    def is_valid_url(cls, url: str, url_type: str = None) -> bool:
        """
        验证URL是否符合指定类型的格式

        Args:
            url: 要验证的URL
            url_type: 指定的URL类型，如果为None则验证是否为任意有效类型

        Returns:
            是否为有效URL

        Examples:
            >>> ShangShiWenUrlParser.is_valid_url("tags_225_1_0_0.html", "tag")
            True
            >>> ShangShiWenUrlParser.is_valid_url("invalid_url.html", "tag")
            False
        """
        if url_type:
            if url_type not in cls.PATTERNS:
                logger.error(f"不支持的URL类型: {url_type}")
                return False
            return bool(re.search(cls.PATTERNS[url_type], url))
        else:
            # 检查是否匹配任意类型
            for pattern in cls.PATTERNS.values():
                if re.search(pattern, url):
                    return True
            return False

    @classmethod
    def get_supported_types(cls) -> list:
        """
        获取支持的URL类型列表

        Returns:
            支持的URL类型列表
        """
        return list(cls.PATTERNS.keys())


# 为了方便使用，提供简化的函数接口
def extract_poem_id(url: str) -> Optional[int]:
    """提取诗词ID的简化接口"""
    return ShangShiWenUrlParser.extract_poem_id(url)


def extract_author_id(url: str) -> Optional[int]:
    """提取作者ID的简化接口"""
    return ShangShiWenUrlParser.extract_author_id(url)


def extract_tag_id(url: str) -> Optional[int]:
    """提取标签ID的简化接口"""
    return ShangShiWenUrlParser.extract_tag_id(url)


def extract_translation_id(url: str) -> Optional[int]:
    """提取翻译ID的简化接口"""
    return ShangShiWenUrlParser.extract_translation_id(url)


def extract_appreciation_id(url: str) -> Optional[int]:
    """提取赏析ID的简化接口"""
    return ShangShiWenUrlParser.extract_appreciation_id(url)


def parse_url(url: str) -> Dict[str, Any]:
    """智能解析URL的简化接口"""
    return ShangShiWenUrlParser.parse_url(url)


if __name__ == "__main__":
    # 测试代码
    test_urls = [
        "http://shangshiwen.com/67827.html",
        "/67827.html",
        "/shiren/785_1.html",
        "tags_225_1_0_0.html",
        "tags_1_1_0_0.html",
        "gwfanyi_4076.html",
        "gwshangxi_5444.html",
        "invalid_url.html"
    ]

    print("=== URL解析测试 ===")
    for url in test_urls:
        result = parse_url(url)
        url_type = result['type'] or 'unknown'
        url_id = result['id'] or 'None'
        print(f"{url:30s} -> {url_type:12s} ID: {url_id}")

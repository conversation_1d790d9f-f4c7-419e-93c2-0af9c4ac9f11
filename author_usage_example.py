#!/usr/bin/env python3
"""
诗人爬虫使用示例
演示不同的数据库操作方式
"""
from utils.db_utils import clear_authors_data, recreate_authors_table, get_table_counts
from author_spider import AuthorSpider
from models import SessionLocal, Author


def example_1_clear_data_only():
    """示例1：只清空作者数据，保留表结构"""
    print("=" * 60)
    print("示例1：只清空作者数据")
    print("=" * 60)
    
    # 清空作者数据
    clear_authors_data()
    
    # 爬取新数据
    spider = AuthorSpider()
    result = spider.crawl_first_author()
    
    if result:
        print(f"✅ 成功爬取: {result['name']}")
    
    # 显示数据库状态
    counts = get_table_counts()
    print("\n数据库状态:")
    for table, count in counts.items():
        if count > 0:
            print(f"  {table}: {count} 条记录")


def example_2_recreate_table():
    """示例2：重新创建作者表（删除表结构并重建）"""
    print("\n" + "=" * 60)
    print("示例2：重新创建作者表")
    print("=" * 60)
    
    # 重新创建表
    recreate_authors_table()
    
    # 爬取新数据
    spider = AuthorSpider()
    result = spider.crawl_first_author()
    
    if result:
        print(f"✅ 成功爬取: {result['name']}")


def example_3_query_with_foreign_key():
    """示例3：演示通过外键关系查询朝代名称"""
    print("\n" + "=" * 60)
    print("示例3：外键关系查询演示")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        authors = db.query(Author).all()
        
        print(f"数据库中共有 {len(authors)} 个作者:")
        for author in authors:
            # 方法1：通过外键关系查询（推荐）
            dynasty_name = author.dynasty.name if author.dynasty else "未知"
            
            print(f"  {author.name} ({dynasty_name}) - ID: {author.author_id}")
            
            # 演示性能：这种查询很快，因为朝代表很小
            print(f"    朝代详情: {author.dynasty.description if author.dynasty else '无'}")
            
    finally:
        db.close()


def main():
    """主函数 - 选择运行哪个示例"""
    print("诗人爬虫使用示例")
    print("请选择要运行的示例:")
    print("1. 只清空作者数据")
    print("2. 重新创建作者表")
    print("3. 外键关系查询演示")
    print("4. 运行所有示例")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        example_1_clear_data_only()
    elif choice == "2":
        example_2_recreate_table()
    elif choice == "3":
        example_3_query_with_foreign_key()
    elif choice == "4":
        example_1_clear_data_only()
        example_2_recreate_table()
        example_3_query_with_foreign_key()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()

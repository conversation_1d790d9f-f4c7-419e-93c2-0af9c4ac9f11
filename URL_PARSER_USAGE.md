# URL解析工具使用指南

## 概述

`utils/url_parser.py` 是一个专门用于解析 shangshiwen.com 网站URL的工具类，可以从各种URL中提取对应的ID。

## 支持的URL类型

| 类型 | URL格式 | 示例 | 提取的ID |
|------|---------|------|----------|
| 诗词 | `/数字.html` | `/67827.html` | 67827 |
| 作者 | `/shiren/数字_1.html` | `/shiren/785_1.html` | 785 |
| 标签 | `tags_数字_1_0_0.html` | `tags_225_1_0_0.html` | 225 |
| 翻译 | `gwfanyi_数字.html` | `gwfanyi_4076.html` | 4076 |
| 赏析 | `gwshangxi_数字.html` | `gwshangxi_5444.html` | 5444 |

## 使用方法

### 1. 导入工具

```python
# 导入类
from utils.url_parser import ShangShiWenUrlParser

# 或导入简化函数
from utils.url_parser import extract_tag_id, extract_poem_id, parse_url
```

### 2. 提取标签ID（你的需求）

```python
# 方法1: 使用类方法
tag_id = ShangShiWenUrlParser.extract_tag_id("tags_225_1_0_0.html")
print(tag_id)  # 输出: 225

# 方法2: 使用简化函数（推荐）
tag_id = extract_tag_id("tags_225_1_0_0.html")
print(tag_id)  # 输出: 225

# 支持完整URL
tag_id = extract_tag_id("http://shangshiwen.com/tags_225_1_0_0.html")
print(tag_id)  # 输出: 225
```

### 3. 提取其他类型的ID

```python
from utils.url_parser import *

# 提取诗词ID
poem_id = extract_poem_id("http://shangshiwen.com/67827.html")
print(poem_id)  # 输出: 67827

# 提取作者ID
author_id = extract_author_id("/shiren/785_1.html")
print(author_id)  # 输出: 785

# 提取翻译ID
translation_id = extract_translation_id("gwfanyi_4076.html")
print(translation_id)  # 输出: 4076

# 提取赏析ID
appreciation_id = extract_appreciation_id("gwshangxi_5444.html")
print(appreciation_id)  # 输出: 5444
```

### 4. 智能解析（自动识别类型）

```python
from utils.url_parser import parse_url

# 自动识别URL类型并提取ID
result = parse_url("tags_225_1_0_0.html")
print(result)
# 输出: {'type': 'tag', 'id': 225, 'url': 'tags_225_1_0_0.html'}

result = parse_url("http://shangshiwen.com/67827.html")
print(result)
# 输出: {'type': 'poem', 'id': 67827, 'url': 'http://shangshiwen.com/67827.html'}

# 处理无效URL
result = parse_url("invalid_url.html")
print(result)
# 输出: {'type': None, 'id': None, 'url': 'invalid_url.html'}
```

### 5. URL验证

```python
from utils.url_parser import ShangShiWenUrlParser

# 验证特定类型的URL
is_valid = ShangShiWenUrlParser.is_valid_url("tags_225_1_0_0.html", "tag")
print(is_valid)  # 输出: True

is_valid = ShangShiWenUrlParser.is_valid_url("invalid.html", "tag")
print(is_valid)  # 输出: False

# 验证是否为任意有效类型
is_valid = ShangShiWenUrlParser.is_valid_url("tags_225_1_0_0.html")
print(is_valid)  # 输出: True
```

## 实际应用示例

### 在爬虫中使用

```python
from utils.url_parser import extract_tag_id, extract_poem_id

class MySpider:
    def parse_poem_page(self, url):
        # 提取诗词ID
        poem_id = extract_poem_id(url)
        if poem_id:
            print(f"正在处理诗词ID: {poem_id}")
        
    def parse_tag_links(self, tag_urls):
        # 批量提取标签ID
        for url in tag_urls:
            tag_id = extract_tag_id(url)
            if tag_id:
                print(f"发现标签ID: {tag_id}")
```

### 批量处理URL

```python
from utils.url_parser import parse_url

urls = [
    "tags_225_1_0_0.html",
    "tags_1_1_0_0.html", 
    "/67827.html",
    "/shiren/785_1.html"
]

for url in urls:
    result = parse_url(url)
    if result['type']:
        print(f"{result['type']} ID: {result['id']} from {url}")
```

## 错误处理

```python
from utils.url_parser import extract_tag_id

# 处理无效URL
tag_id = extract_tag_id("invalid_url.html")
if tag_id is None:
    print("无法提取标签ID")
else:
    print(f"标签ID: {tag_id}")

# 使用默认值
tag_id = extract_tag_id("invalid_url.html") or 0
print(f"标签ID: {tag_id}")  # 输出: 标签ID: 0
```

## 测试

运行测试脚本验证功能：

```bash
# 测试URL解析工具
python test_url_parser.py

# 测试工具本身
python utils/url_parser.py
```

## 特殊说明

### 标签URL格式分析

标签URL格式：`tags_{id}_1_0_0.html`

- **变化部分**: 第一个数字（标签ID）
- **固定部分**: `_1_0_0.html` 后缀
- **ID范围**: 1-473（不连续）

例如：
- `tags_1_1_0_0.html` → ID: 1
- `tags_225_1_0_0.html` → ID: 225
- `tags_473_1_0_0.html` → ID: 473

### 日志输出

工具会输出调试日志，可以通过loguru配置控制：

```python
from loguru import logger

# 禁用调试日志
logger.remove()
logger.add(sys.stdout, level="INFO")
```

## 性能说明

- 所有解析操作都是基于正则表达式，性能很高
- 支持批量处理大量URL
- 内存占用极小
- 无网络请求，纯本地解析

## 扩展性

如果需要支持新的URL格式，只需在 `PATTERNS` 字典中添加新的正则表达式模式，并添加对应的提取方法即可。

#!/usr/bin/env python3
"""
author_spider.py 的增强补丁
只需要在原文件中添加几行代码即可获得反爬功能
"""

# 1. 在 author_spider.py 文件顶部添加这些导入
"""
在 author_spider.py 的导入部分添加：

import time
import random
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from ratelimit import limits, sleep_and_retry
from fake_useragent import UserAgent
from config import ANTI_DETECTION_CONFIG
"""

# 2. 修改 AuthorSpider 类的 __init__ 方法
"""
在 __init__ 方法中添加：

def __init__(self):
    self.session = requests.Session()
    self.ua = UserAgent()  # 添加这行
    self.setup_session()
    self.base_url = "http://shangshiwen.com"
    
    # 统计信息 - 添加这些行
    self.request_count = 0
    self.success_count = 0
    self.failure_count = 0
    
    # ... 其他原有代码保持不变
"""

# 3. 修改 setup_session 方法
"""
def setup_session(self):
    \"\"\"设置会话\"\"\"
    headers = {
        'User-Agent': self.ua.random,  # 改为随机UA
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
    }
    self.session.headers.update(headers)
    self.session.timeout = 30
"""

# 4. 添加增强的请求方法
"""
在 AuthorSpider 类中添加这个新方法：

@sleep_and_retry
@limits(calls=int(ANTI_DETECTION_CONFIG['throttle']['requests_per_minute']), period=60)
@retry(
    stop=stop_after_attempt(ANTI_DETECTION_CONFIG['retry']['max_attempts']),
    wait=wait_exponential(
        multiplier=ANTI_DETECTION_CONFIG['retry']['base_delay'],
        max=ANTI_DETECTION_CONFIG['retry']['max_delay']
    ),
    retry=retry_if_exception_type((requests.RequestException, ConnectionError))
)
def enhanced_get(self, url, **kwargs):
    \"\"\"增强版GET请求\"\"\"
    try:
        # 记录请求
        self.request_count += 1
        
        # 随机延迟
        delay = random.uniform(
            ANTI_DETECTION_CONFIG['throttle']['min_delay'],
            ANTI_DETECTION_CONFIG['throttle']['min_delay'] * 2
        )
        time.sleep(delay)
        
        # 每隔一段时间更新UA
        if self.request_count % 10 == 0:
            self.session.headers['User-Agent'] = self.ua.random
        
        # 发起请求
        response = self.session.get(url, **kwargs)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        # 记录成功
        self.success_count += 1
        logger.info(f"✅ 请求成功: {url}")
        return response
        
    except requests.RequestException as e:
        # 记录失败
        self.failure_count += 1
        logger.warning(f"❌ 请求失败: {url} - {e}")
        raise e

def get_stats(self):
    \"\"\"获取统计信息\"\"\"
    success_rate = self.success_count / self.request_count if self.request_count > 0 else 0
    return {
        'total_requests': self.request_count,
        'successful_requests': self.success_count,
        'failed_requests': self.failure_count,
        'success_rate': success_rate
    }

def print_stats(self):
    \"\"\"打印统计信息\"\"\"
    stats = self.get_stats()
    logger.info("=== 请求统计信息 ===")
    logger.info(f"总请求数: {stats['total_requests']}")
    logger.info(f"成功请求: {stats['successful_requests']}")
    logger.info(f"失败请求: {stats['failed_requests']}")
    logger.info(f"成功率: {stats['success_rate']:.2%}")
"""

# 5. 替换所有的 self.session.get() 调用
"""
将所有的：
    response = self.session.get(url)

替换为：
    response = self.enhanced_get(url)

具体位置：
1. parse_author_list_page 方法中的第60行
2. parse_author_detail_page 方法中的第131行  
3. download_image 方法中的第200行
"""

print("""
🔧 author_spider.py 增强改造指南

有两种方案可以选择：

方案1：使用新的 enhanced_author_spider.py（推荐）
- ✅ 完全集成反爬功能
- ✅ 继承所有增强特性
- ✅ 不影响原文件
- ✅ 更好的错误处理和统计

使用方法：
```python
from enhanced_author_spider import EnhancedAuthorSpider

spider = EnhancedAuthorSpider()
results = spider.crawl_author_list("http://shangshiwen.com/authors/", max_authors=10)
```

方案2：最小修改原文件
- 只需要修改几个地方
- 保持原有代码结构
- 添加基础反爬功能

修改步骤：
1. 添加导入语句
2. 修改 __init__ 和 setup_session 方法
3. 添加 enhanced_get 方法
4. 替换 self.session.get() 为 self.enhanced_get()

推荐使用方案1，因为它提供了更完整的功能和更好的代码组织。
""")

#!/usr/bin/env python3
"""
代理增强爬虫 - 第二阶段实施
集成代理轮换、智能重试、请求监控等高级功能
"""
import time
import random
import requests
from typing import Optional, Dict, Any, List
from loguru import logger
from config import ANTI_DETECTION_CONFIG
from utils.proxy_manager import ProxyManager, FreeProxyFetcher
from enhanced_spider_simple import SimpleEnhancedSpider


class ProxyEnhancedSpider(SimpleEnhancedSpider):
    """代理增强爬虫"""
    
    def __init__(self, enable_proxy=True, proxy_sources='auto'):
        # 初始化代理管理器
        self.proxy_manager = None
        self.enable_proxy = enable_proxy
        self.proxy_sources = proxy_sources
        
        if enable_proxy:
            self.setup_proxy_manager()
        
        # 调用父类初始化
        super().__init__()
        
        # 代理统计
        self.proxy_requests = 0
        self.proxy_failures = 0
        
        logger.info(f"代理增强爬虫初始化完成 (代理: {'启用' if enable_proxy else '禁用'})")
    
    def setup_proxy_manager(self):
        """设置代理管理器"""
        logger.info("初始化代理管理器...")
        
        self.proxy_manager = ProxyManager()
        
        if self.proxy_sources == 'auto':
            # 自动获取免费代理
            self.load_free_proxies()
        elif self.proxy_sources == 'premium':
            # 加载付费代理配置
            self.load_premium_proxies()
        elif isinstance(self.proxy_sources, list):
            # 使用自定义代理列表
            self.proxy_manager.load_proxies(self.proxy_sources)
        
        # 检查代理可用性
        if self.proxy_manager.proxies:
            logger.info("检查代理可用性...")
            results = self.proxy_manager.check_all_proxies(max_workers=5)
            
            stats = self.proxy_manager.get_stats()
            if stats['active_proxies'] > 0:
                logger.success(f"代理管理器初始化成功: {stats['active_proxies']} 个可用代理")
            else:
                logger.warning("没有可用的代理，将禁用代理功能")
                self.enable_proxy = False
                self.proxy_manager = None
        else:
            logger.warning("没有加载任何代理")
            self.enable_proxy = False
            self.proxy_manager = None
    
    def load_free_proxies(self):
        """加载免费代理"""
        logger.info("获取免费代理...")
        
        try:
            # 从所有源获取代理
            all_proxies = FreeProxyFetcher.fetch_all_sources()
            
            if all_proxies:
                # 只取前30个，避免检查时间过长
                selected_proxies = all_proxies[:30]
                self.proxy_manager.load_proxies(selected_proxies)
                logger.info(f"加载了 {len(selected_proxies)} 个免费代理")
            else:
                logger.warning("没有获取到免费代理")
                
        except Exception as e:
            logger.error(f"加载免费代理失败: {e}")
    
    def load_premium_proxies(self):
        """加载付费代理配置"""
        logger.info("加载付费代理配置...")
        
        # 这里应该从配置文件或环境变量读取付费代理信息
        premium_proxies = FreeProxyFetcher.get_premium_proxy_config()
        
        if premium_proxies:
            self.proxy_manager.load_proxies(premium_proxies)
            logger.info(f"加载了 {len(premium_proxies)} 个付费代理")
        else:
            logger.warning("没有配置付费代理")
    
    def get_proxy_for_request(self) -> Optional[Dict[str, str]]:
        """获取用于请求的代理配置"""
        if not self.enable_proxy or not self.proxy_manager:
            return None
        
        proxy_info = self.proxy_manager.get_proxy('round_robin')
        if proxy_info:
            return {
                'http': proxy_info.url,
                'https': proxy_info.url
            }
        return None
    
    def enhanced_get_page(self, url: str, **kwargs) -> Optional[requests.Response]:
        """增强版页面获取方法（支持代理）"""
        max_proxy_retries = 3
        proxy_retry_count = 0
        
        while proxy_retry_count < max_proxy_retries:
            try:
                # 获取代理配置
                proxy_config = self.get_proxy_for_request()
                if proxy_config:
                    kwargs['proxies'] = proxy_config
                    self.proxy_requests += 1
                
                # 调用父类方法
                response = super().enhanced_get_page(url, **kwargs)
                
                # 如果使用了代理且成功，更新代理统计
                if proxy_config and response and self.proxy_manager:
                    proxy_info = self.proxy_manager.get_proxy()
                    if proxy_info:
                        self.proxy_manager.mark_success(proxy_info)
                
                return response
                
            except Exception as e:
                # 如果是代理相关错误，尝试换代理
                if proxy_config and self.proxy_manager:
                    proxy_info = self.proxy_manager.get_proxy()
                    if proxy_info:
                        self.proxy_manager.mark_failure(proxy_info)
                    
                    self.proxy_failures += 1
                    proxy_retry_count += 1
                    
                    logger.warning(f"代理请求失败，尝试换代理 ({proxy_retry_count}/{max_proxy_retries}): {e}")
                    
                    # 如果还有重试机会，继续
                    if proxy_retry_count < max_proxy_retries:
                        time.sleep(random.uniform(1, 3))
                        continue
                
                # 如果不是代理错误或重试次数用完，抛出异常
                raise e
        
        # 所有代理重试都失败了
        logger.error(f"所有代理重试都失败: {url}")
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息（包含代理统计）"""
        stats = super().get_stats()
        
        # 添加代理统计
        if self.enable_proxy and self.proxy_manager:
            proxy_stats = self.proxy_manager.get_stats()
            stats.update({
                'proxy_enabled': True,
                'proxy_requests': self.proxy_requests,
                'proxy_failures': self.proxy_failures,
                'proxy_success_rate': (self.proxy_requests - self.proxy_failures) / self.proxy_requests if self.proxy_requests > 0 else 0,
                'active_proxies': proxy_stats['active_proxies'],
                'total_proxies': proxy_stats['total_proxies']
            })
        else:
            stats['proxy_enabled'] = False
        
        return stats
    
    def print_stats(self):
        """打印统计信息（包含代理信息）"""
        stats = self.get_stats()
        
        logger.info("=== 代理增强爬虫统计信息 ===")
        logger.info(f"运行时间: {stats['runtime']:.2f}秒")
        logger.info(f"总请求数: {stats['total_requests']}")
        logger.info(f"成功请求: {stats['successful_requests']}")
        logger.info(f"失败请求: {stats['failed_requests']}")
        logger.info(f"成功率: {stats['success_rate']:.2%}")
        logger.info(f"请求频率: {stats['requests_per_minute']:.2f}/分钟")
        
        if stats['proxy_enabled']:
            logger.info(f"代理请求数: {stats['proxy_requests']}")
            logger.info(f"代理失败数: {stats['proxy_failures']}")
            logger.info(f"代理成功率: {stats['proxy_success_rate']:.2%}")
            logger.info(f"可用代理: {stats['active_proxies']}/{stats['total_proxies']}")
        else:
            logger.info("代理功能: 未启用")
    
    def refresh_proxies(self):
        """刷新代理列表"""
        if not self.enable_proxy:
            logger.warning("代理功能未启用")
            return
        
        logger.info("刷新代理列表...")
        
        # 移除失效代理
        if self.proxy_manager:
            self.proxy_manager.remove_inactive_proxies()
        
        # 重新获取代理
        if self.proxy_sources == 'auto':
            self.load_free_proxies()
        
        # 重新检查
        if self.proxy_manager and self.proxy_manager.proxies:
            self.proxy_manager.check_all_proxies(max_workers=5)
            stats = self.proxy_manager.get_stats()
            logger.info(f"代理刷新完成: {stats['active_proxies']} 个可用代理")
    
    def adaptive_proxy_crawl(self, urls: List[str]):
        """自适应代理爬取"""
        logger.info(f"🤖 开始自适应代理爬取 {len(urls)} 个URL")
        
        results = []
        proxy_refresh_threshold = 10  # 每10次失败刷新代理
        
        for i, url in enumerate(urls, 1):
            try:
                # 检查代理失败率，决定是否刷新代理
                if (self.proxy_failures > 0 and 
                    self.proxy_failures % proxy_refresh_threshold == 0 and
                    self.enable_proxy):
                    
                    logger.info("代理失败率较高，刷新代理列表...")
                    self.refresh_proxies()
                
                # 爬取
                result = self.crawl_poem(url)
                
                if result:
                    results.append(result)
                    logger.success(f"[{i}/{len(urls)}] ✅ {result['title']}")
                
                # 每20个请求显示统计
                if i % 20 == 0:
                    self.print_stats()
                
            except KeyboardInterrupt:
                logger.info("用户中断爬取")
                break
            except Exception as e:
                logger.error(f"爬取异常: {e}")
        
        logger.info(f"🏁 自适应代理爬取完成: 成功 {len(results)} 个")
        self.print_stats()
        
        return results


def demo_proxy_spider():
    """代理爬虫示例"""
    logger.info("=== 代理增强爬虫示例 ===")
    
    # 创建代理爬虫
    spider = ProxyEnhancedSpider(
        enable_proxy=True,
        proxy_sources='auto'  # 自动获取免费代理
    )
    
    try:
        # 测试单个URL
        test_url = "http://shangshiwen.com/67827.html"
        result = spider.crawl_poem_with_stats(test_url)
        
        if result:
            logger.success(f"✅ 代理爬取成功: {result['title']}")
        
    except Exception as e:
        logger.error(f"代理爬取异常: {e}")
    
    finally:
        spider.print_stats()


def demo_batch_proxy_crawl():
    """批量代理爬取示例"""
    logger.info("=== 批量代理爬取示例 ===")
    
    spider = ProxyEnhancedSpider(enable_proxy=True)
    
    try:
        test_urls = [
            "http://shangshiwen.com/67827.html",
            "http://shangshiwen.com/67828.html",
            "http://shangshiwen.com/67829.html",
            "http://shangshiwen.com/67830.html",
            "http://shangshiwen.com/67831.html"
        ]
        
        results = spider.adaptive_proxy_crawl(test_urls)
        logger.info(f"🏁 批量代理爬取完成，成功 {len(results)} 个")
        
    except Exception as e:
        logger.error(f"批量代理爬取异常: {e}")
    
    finally:
        spider.print_stats()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(
        "logs/proxy_enhanced_spider.log",
        rotation="10 MB",
        retention="7 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}\n"
    )
    
    try:
        # 运行示例
        demo_proxy_spider()
        # demo_batch_proxy_crawl()  # 可以取消注释测试批量爬取
        
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        logger.info("程序结束")

#!/usr/bin/env python3
"""
简化版增强爬虫 - 第一阶段实施
集成基础反爬策略：智能重试、请求频率控制、增强请求头
"""
import time
import random
import requests
from typing import Optional, Dict, Any
from fake_useragent import UserAgent
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from ratelimit import limits, sleep_and_retry
from config import ANTI_DETECTION_CONFIG
from shangshiwen_spider import ShangShiWenSpider


class SimpleEnhancedSpider(ShangShiWenSpider):
    """简化版增强爬虫 - 第一阶段实施"""
    
    def __init__(self):
        # 调用父类初始化
        super().__init__()
        
        # 统计信息
        self.request_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.start_time = time.time()
        
        # 重新设置会话，添加更多反检测特性
        self.setup_enhanced_session()
        
        logger.info("简化版增强爬虫初始化完成")
    
    def setup_enhanced_session(self):
        """设置增强版会话"""
        # 创建新的会话
        self.session = requests.Session()
        
        # 设置连接池
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=10,
            max_retries=0  # 我们自己处理重试
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 设置超时
        self.session.timeout = 30
        
        # 设置基础请求头
        self.update_headers()
        
        logger.info("增强版会话设置完成")
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        headers = {}
        
        # 随机User-Agent
        headers['User-Agent'] = self.ua.random
        
        # 随机Accept-Language
        languages = [
            'zh-CN,zh;q=0.9,en;q=0.8',
            'zh-CN,zh;q=0.8,en;q=0.7',
            'en-US,en;q=0.9,zh;q=0.8',
            'zh-CN,zh;q=0.9',
        ]
        headers['Accept-Language'] = random.choice(languages)
        
        # 基础请求头
        headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        })
        
        # 随机添加DNT头
        if random.random() < 0.3:
            headers['DNT'] = '1'
        
        return headers
    
    def update_headers(self):
        """更新会话请求头"""
        headers = self.get_random_headers()
        self.session.headers.update(headers)
    
    @sleep_and_retry
    @limits(calls=int(ANTI_DETECTION_CONFIG['throttle']['requests_per_minute']), 
            period=60)  # 每分钟限制请求数
    @retry(
        stop=stop_after_attempt(ANTI_DETECTION_CONFIG['retry']['max_attempts']),
        wait=wait_exponential(
            multiplier=ANTI_DETECTION_CONFIG['retry']['base_delay'],
            max=ANTI_DETECTION_CONFIG['retry']['max_delay']
        ),
        retry=retry_if_exception_type((requests.RequestException, ConnectionError))
    )
    def enhanced_get_page(self, url: str, **kwargs) -> Optional[requests.Response]:
        """增强版页面获取方法"""
        try:
            # 记录请求
            self.request_count += 1
            
            # 随机延迟
            base_delay = ANTI_DETECTION_CONFIG['throttle']['min_delay']
            max_delay = ANTI_DETECTION_CONFIG['throttle']['max_delay']
            delay = random.uniform(base_delay, min(base_delay * 2, max_delay))
            time.sleep(delay)
            
            # 每隔一段时间更新请求头
            if self.request_count % 10 == 0:
                self.update_headers()
            
            # 发起请求
            start_time = time.time()
            response = self.session.get(url, **kwargs)
            response_time = time.time() - start_time
            
            # 检查响应
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 记录成功
            self.success_count += 1
            
            logger.info(f"✅ 请求成功: {url} ({response_time:.2f}s)")
            return response
            
        except requests.RequestException as e:
            # 记录失败
            self.failure_count += 1
            logger.warning(f"❌ 请求失败: {url} - {e}")
            
            # 特殊状态码处理
            if hasattr(e, 'response') and e.response is not None:
                status_code = e.response.status_code
                if status_code == 429:  # Too Many Requests
                    logger.warning("触发限流，增加延迟")
                    time.sleep(random.uniform(5, 10))
                elif status_code == 403:  # Forbidden
                    logger.warning("访问被拒绝，更新请求头")
                    self.update_headers()
                    time.sleep(random.uniform(2, 5))
            
            raise e
    
    def get_page(self, url: str, **kwargs) -> Optional[requests.Response]:
        """重写父类的get_page方法"""
        return self.enhanced_get_page(url, **kwargs)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        runtime = time.time() - self.start_time
        success_rate = self.success_count / self.request_count if self.request_count > 0 else 0
        
        return {
            'runtime': runtime,
            'total_requests': self.request_count,
            'successful_requests': self.success_count,
            'failed_requests': self.failure_count,
            'success_rate': success_rate,
            'requests_per_minute': (self.request_count / runtime) * 60 if runtime > 0 else 0
        }
    
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        
        logger.info("=== 爬虫统计信息 ===")
        logger.info(f"运行时间: {stats['runtime']:.2f}秒")
        logger.info(f"总请求数: {stats['total_requests']}")
        logger.info(f"成功请求: {stats['successful_requests']}")
        logger.info(f"失败请求: {stats['failed_requests']}")
        logger.info(f"成功率: {stats['success_rate']:.2%}")
        logger.info(f"请求频率: {stats['requests_per_minute']:.2f}/分钟")
    
    def crawl_poem_with_stats(self, poem_url: str):
        """爬取诗词并显示统计信息"""
        logger.info(f"🕷️  开始爬取诗词: {poem_url}")
        
        start_time = time.time()
        result = self.crawl_poem(poem_url)
        end_time = time.time()
        
        if result:
            logger.success(f"🎉 爬取成功: {result['title']} - {result['author_name']} ({end_time - start_time:.2f}s)")
        else:
            logger.error(f"❌ 爬取失败: {poem_url}")
        
        return result
    
    def batch_crawl_with_monitoring(self, urls: list, max_failures: int = 5):
        """批量爬取，带监控功能"""
        logger.info(f"🚀 开始批量爬取 {len(urls)} 个URL")
        
        results = []
        consecutive_failures = 0
        
        for i, url in enumerate(urls, 1):
            try:
                # 检查连续失败次数
                if consecutive_failures >= max_failures:
                    logger.error(f"连续失败 {consecutive_failures} 次，停止爬取")
                    break
                
                # 爬取单个URL
                result = self.crawl_poem(url)
                
                if result:
                    results.append(result)
                    consecutive_failures = 0  # 重置连续失败计数
                    logger.success(f"[{i}/{len(urls)}] ✅ 成功: {result['title']}")
                else:
                    consecutive_failures += 1
                    logger.error(f"[{i}/{len(urls)}] ❌ 失败: {url}")
                
                # 每10个请求显示一次统计
                if i % 10 == 0:
                    self.print_stats()
                
            except KeyboardInterrupt:
                logger.info("用户中断爬取")
                break
            except Exception as e:
                consecutive_failures += 1
                logger.error(f"爬取异常: {e}")
        
        # 最终统计
        logger.info(f"🏁 批量爬取完成: 成功 {len(results)} 个，失败 {len(urls) - len(results)} 个")
        self.print_stats()
        
        return results
    
    def adaptive_crawl(self, urls: list):
        """自适应爬取 - 根据成功率动态调整策略"""
        logger.info(f"🤖 开始自适应爬取 {len(urls)} 个URL")
        
        results = []
        
        for i, url in enumerate(urls, 1):
            try:
                # 根据当前成功率调整延迟
                if self.request_count >= 10:
                    success_rate = self.success_count / self.request_count
                    
                    if success_rate < 0.5:
                        # 成功率低，增加延迟
                        extra_delay = random.uniform(3, 8)
                        logger.info(f"成功率较低({success_rate:.2%})，增加延迟 {extra_delay:.1f}s")
                        time.sleep(extra_delay)
                    elif success_rate > 0.9:
                        # 成功率高，可以稍微加快
                        logger.debug(f"成功率较高({success_rate:.2%})，保持当前速度")
                
                # 爬取
                result = self.crawl_poem(url)
                
                if result:
                    results.append(result)
                    logger.success(f"[{i}/{len(urls)}] ✅ {result['title']}")
                
            except KeyboardInterrupt:
                logger.info("用户中断爬取")
                break
            except Exception as e:
                logger.error(f"爬取异常: {e}")
        
        logger.info(f"🏁 自适应爬取完成: 成功 {len(results)} 个")
        self.print_stats()
        
        return results


def demo_simple_usage():
    """简单使用示例"""
    logger.info("=== 简化版增强爬虫示例 ===")
    
    spider = SimpleEnhancedSpider()
    
    try:
        # 测试单个URL
        test_url = "http://shangshiwen.com/67827.html"
        result = spider.crawl_poem_with_stats(test_url)
        
        if result:
            logger.success(f"✅ 爬取成功: {result['title']} - {result['author_name']}")
        
    except Exception as e:
        logger.error(f"爬取异常: {e}")
    
    finally:
        spider.print_stats()


def demo_batch_crawl():
    """批量爬取示例"""
    logger.info("=== 批量爬取示例 ===")
    
    spider = SimpleEnhancedSpider()
    
    try:
        # 测试多个URL
        test_urls = [
            "http://shangshiwen.com/67827.html",
            "http://shangshiwen.com/67828.html", 
            "http://shangshiwen.com/67829.html",
            "http://shangshiwen.com/67830.html",
            "http://shangshiwen.com/67831.html"
        ]
        
        results = spider.batch_crawl_with_monitoring(test_urls)
        logger.info(f"🏁 批量爬取完成，成功 {len(results)} 个")
        
    except Exception as e:
        logger.error(f"批量爬取异常: {e}")
    
    finally:
        spider.print_stats()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(
        "logs/enhanced_spider_simple.log",
        rotation="10 MB",
        retention="7 days", 
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}\n"
    )
    
    try:
        # 显示当前配置
        logger.info("当前反爬配置:")
        logger.info(f"  重试次数: {ANTI_DETECTION_CONFIG['retry']['max_attempts']}")
        logger.info(f"  每分钟请求数: {ANTI_DETECTION_CONFIG['throttle']['requests_per_minute']}")
        logger.info(f"  最小延迟: {ANTI_DETECTION_CONFIG['throttle']['min_delay']}s")
        
        # 运行示例
        demo_simple_usage()
        # demo_batch_crawl()  # 可以取消注释测试批量爬取
        
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        logger.info("程序结束")

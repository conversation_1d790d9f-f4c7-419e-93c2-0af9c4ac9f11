# 反爬策略使用指南

本指南详细介绍如何使用项目中的反爬策略功能，帮助你应对各种反爬挑战。

## 📋 目录

1. [快速开始](#快速开始)
2. [配置说明](#配置说明)
3. [功能模块](#功能模块)
4. [使用示例](#使用示例)
5. [最佳实践](#最佳实践)
6. [故障排除](#故障排除)

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装浏览器驱动（如果使用浏览器自动化）
playwright install
```

### 2. 基础使用

```python
from enhanced_spider_example import EnhancedShangShiWenSpider

# 创建增强版爬虫
spider = EnhancedShangShiWenSpider(
    use_proxy=False,           # 是否使用代理
    use_advanced_features=True # 是否启用高级功能
)

# 爬取单个页面
result = spider.crawl_poem_with_stats("http://shangshiwen.com/67827.html")
```

### 3. 环境变量配置

创建 `.env` 文件：

```env
# 基础配置
REQUEST_DELAY=1.0
RETRY_MAX_ATTEMPTS=3

# 代理配置
PROXY_ENABLED=false
PROXY_STRATEGY=round_robin

# 限流配置
THROTTLE_RPS=1.0
THROTTLE_RPM=60
THROTTLE_ADAPTIVE=true

# 监控配置
MONITORING_ENABLED=true
MONITORING_LOG_INTERVAL=100
```

## ⚙️ 配置说明

### 代理配置

```python
ANTI_DETECTION_CONFIG['proxy'] = {
    'enabled': False,                    # 是否启用代理
    'rotation_strategy': 'round_robin',  # 轮换策略: round_robin, random, best_performance
    'check_url': 'http://httpbin.org/ip', # 代理检测URL
    'health_check_interval': 300,        # 健康检查间隔(秒)
    'max_failures': 5,                   # 最大失败次数
}
```

### 重试策略配置

```python
ANTI_DETECTION_CONFIG['retry'] = {
    'max_attempts': 3,        # 最大重试次数
    'base_delay': 1.0,        # 基础延迟时间
    'max_delay': 60.0,        # 最大延迟时间
    'strategy': 'exponential', # 重试策略: fixed, linear, exponential, random
    'backoff_factor': 2.0,    # 退避因子
    'jitter': True,           # 是否添加随机抖动
}
```

### 请求频率控制

```python
ANTI_DETECTION_CONFIG['throttle'] = {
    'requests_per_second': 1.0,  # 每秒请求数
    'requests_per_minute': 60,   # 每分钟请求数
    'requests_per_hour': 3600,   # 每小时请求数
    'burst_size': 5,             # 突发请求数量
    'adaptive': True,            # 自适应调整
    'min_delay': 0.5,            # 最小延迟
    'max_delay': 10.0,           # 最大延迟
}
```

## 🔧 功能模块

### 1. 代理管理器 (ProxyManager)

```python
from utils.proxy_manager import ProxyManager, FreeProxyFetcher

# 创建代理管理器
proxy_manager = ProxyManager()

# 添加代理
proxy_manager.add_proxy('127.0.0.1', 8080, protocol='http')

# 获取免费代理
free_proxies = FreeProxyFetcher.fetch_from_proxylist()
proxy_manager.load_proxies(free_proxies)

# 检查代理可用性
proxy_manager.check_all_proxies()

# 获取统计信息
stats = proxy_manager.get_stats()
print(f"可用代理: {stats['active_proxies']}/{stats['total_proxies']}")
```

### 2. 重试处理器 (RetryHandler)

```python
from utils.retry_handler import RetryHandler, RetryConfig, retry_on_failure

# 使用装饰器
@retry_on_failure(max_attempts=3, base_delay=1.0)
def fetch_data(url):
    response = requests.get(url)
    response.raise_for_status()
    return response.text

# 直接使用重试处理器
config = RetryConfig(max_attempts=5, strategy='exponential')
handler = RetryHandler(config)
result = handler.retry(fetch_data, url)
```

### 3. 请求频率控制器 (RequestThrottler)

```python
from utils.request_throttler import RequestThrottler, ThrottleConfig

# 创建限流器
config = ThrottleConfig(requests_per_second=2.0, adaptive=True)
throttler = RequestThrottler(config)

# 在请求前等待
throttler.wait_if_needed()

# 记录请求结果
throttler.record_request(response_time=1.5, success=True)

# 获取统计信息
stats = throttler.get_stats()
```

### 4. 反检测爬虫 (AntiDetectionSpider)

```python
from utils.anti_detection_spider import AntiDetectionSpider

# 创建反检测爬虫
spider = AntiDetectionSpider(
    proxy_manager=proxy_manager,
    retry_config=retry_config,
    throttle_config=throttle_config
)

# 发起请求
response = spider.get("http://example.com")

# 获取统计信息
spider.print_stats()
```

### 5. 浏览器自动化 (BrowserSpider)

```python
from utils.browser_spider import BrowserSpider

# 创建浏览器爬虫
spider = BrowserSpider(browser='chrome', headless=True)

try:
    # 访问页面
    spider.get_page("http://example.com")
    
    # 获取元素文本
    title = spider.get_text("h1")
    
    # 点击元素
    spider.click_element("button.submit")
    
    # 输入文本
    spider.input_text("input[name='search']", "关键词")
    
finally:
    spider.close()
```

## 📝 使用示例

### 示例1: 基础反爬策略

```python
from enhanced_spider_example import EnhancedShangShiWenSpider

# 创建爬虫（启用基础反爬功能）
spider = EnhancedShangShiWenSpider(use_advanced_features=True)

try:
    # 爬取单个页面
    result = spider.crawl_poem_with_stats("http://shangshiwen.com/67827.html")
    
    if result:
        print(f"成功爬取: {result['title']}")
    
finally:
    spider.cleanup()
```

### 示例2: 使用代理

```python
# 启用代理功能
spider = EnhancedShangShiWenSpider(
    use_proxy=True,
    use_advanced_features=True
)

try:
    # 批量爬取
    urls = [
        "http://shangshiwen.com/67827.html",
        "http://shangshiwen.com/67828.html",
        "http://shangshiwen.com/67829.html"
    ]
    
    results = spider.batch_crawl_with_monitoring(urls)
    print(f"批量爬取完成: {len(results)} 个成功")
    
finally:
    spider.cleanup()
```

### 示例3: 自定义配置

```python
from utils.proxy_manager import ProxyManager
from utils.retry_handler import RetryConfig, RetryStrategy
from utils.request_throttler import ThrottleConfig
from utils.anti_detection_spider import AntiDetectionSpider

# 自定义代理管理器
proxy_manager = ProxyManager()
proxy_manager.add_proxy('proxy1.example.com', 8080)
proxy_manager.add_proxy('proxy2.example.com', 8080)

# 自定义重试配置
retry_config = RetryConfig(
    max_attempts=5,
    base_delay=2.0,
    strategy=RetryStrategy.EXPONENTIAL,
    backoff_factor=1.5
)

# 自定义限流配置
throttle_config = ThrottleConfig(
    requests_per_second=0.5,  # 更保守的请求频率
    adaptive=True,
    min_delay=2.0
)

# 创建自定义爬虫
spider = AntiDetectionSpider(
    proxy_manager=proxy_manager,
    retry_config=retry_config,
    throttle_config=throttle_config.__dict__
)

try:
    response = spider.get("http://example.com")
    if response:
        print(f"响应状态: {response.status_code}")
    
finally:
    spider.cleanup()
```

## 💡 最佳实践

### 1. 请求频率控制

- **保守策略**: 每秒1-2个请求，避免触发限流
- **自适应调整**: 启用自适应功能，根据响应情况动态调整
- **突发控制**: 限制短时间内的突发请求数量

### 2. 代理使用

- **代理质量**: 优先使用高质量的付费代理
- **健康检查**: 定期检查代理可用性，及时移除失效代理
- **轮换策略**: 根据需求选择合适的轮换策略

### 3. 错误处理

- **分类处理**: 根据错误类型采用不同的重试策略
- **熔断机制**: 连续失败时暂停请求，避免被封禁
- **降级策略**: 失败时使用备用方案

### 4. 监控告警

- **实时监控**: 监控请求成功率、响应时间等指标
- **异常告警**: 设置合理的告警阈值，及时发现问题
- **日志记录**: 详细记录请求和响应信息，便于问题排查

### 5. 请求头优化

- **随机化**: 随机化User-Agent、Accept-Language等请求头
- **真实性**: 使用真实浏览器的请求头组合
- **一致性**: 保持同一会话内请求头的一致性

## 🔍 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理地址和端口是否正确
   - 验证代理是否需要认证
   - 测试代理的网络连通性

2. **请求频率过快被限流**
   - 降低请求频率设置
   - 启用自适应调整功能
   - 增加随机延迟

3. **重试次数过多**
   - 检查目标网站是否正常
   - 调整重试策略和延迟时间
   - 验证请求参数是否正确

4. **浏览器驱动问题**
   - 确保浏览器驱动版本匹配
   - 检查浏览器是否正确安装
   - 尝试使用不同的浏览器

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **监控网络请求**
   ```python
   spider.print_stats()  # 查看请求统计
   ```

3. **测试单个功能**
   ```python
   # 单独测试代理
   proxy_manager.check_all_proxies()
   
   # 单独测试重试
   handler.retry(test_function)
   ```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件 `logs/spider.log`
2. 检查配置是否正确
3. 参考示例代码
4. 提交Issue并附上详细的错误信息

---

**注意**: 请遵守目标网站的robots.txt和使用条款，合理使用反爬策略，避免对服务器造成过大压力。

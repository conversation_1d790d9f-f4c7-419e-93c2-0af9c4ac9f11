#!/usr/bin/env python3
"""
增强版爬虫示例 - 展示如何使用反爬策略
"""
import time
from loguru import logger
from config import ANTI_DETECTION_CONFIG
from utils.proxy_manager import Proxy<PERSON>anager, FreeProxyFetcher
from utils.retry_handler import RetryConfig, RetryStrategy
from utils.request_throttler import ThrottleConfig
from utils.anti_detection_spider import AntiDetectionSpider
from shangshiwen_spider import ShangShiWenSpider


class EnhancedShangShiWenSpider(ShangShiWenSpider):
    """增强版尚诗文网爬虫 - 集成反爬策略"""
    
    def __init__(self, use_proxy=False, use_advanced_features=True):
        # 初始化代理管理器
        self.proxy_manager = None
        if use_proxy:
            self.proxy_manager = self._setup_proxy_manager()
        
        # 初始化反检测爬虫
        if use_advanced_features:
            self.anti_spider = self._setup_anti_detection_spider()
        else:
            self.anti_spider = None
        
        # 调用父类初始化
        super().__init__()
        
        # 如果使用高级功能，替换session
        if self.anti_spider:
            self.session = self.anti_spider.session
    
    def _setup_proxy_manager(self):
        """设置代理管理器"""
        logger.info("初始化代理管理器...")
        
        proxy_manager = ProxyManager()
        
        # 尝试获取免费代理
        try:
            free_proxies = FreeProxyFetcher.fetch_from_proxylist()
            if free_proxies:
                proxy_manager.load_proxies(free_proxies[:10])  # 只使用前10个
                logger.info(f"加载了 {len(free_proxies[:10])} 个免费代理")
            else:
                # 使用示例代理
                sample_proxies = FreeProxyFetcher.get_sample_proxies()
                proxy_manager.load_proxies(sample_proxies)
                logger.warning("使用示例代理配置")
        except Exception as e:
            logger.error(f"获取代理失败: {e}")
            return None
        
        # 检查代理可用性
        proxy_manager.check_all_proxies(max_workers=5)
        
        stats = proxy_manager.get_stats()
        if stats['active_proxies'] == 0:
            logger.warning("没有可用的代理，禁用代理功能")
            return None
        
        logger.success(f"代理管理器初始化完成，{stats['active_proxies']} 个代理可用")
        return proxy_manager
    
    def _setup_anti_detection_spider(self):
        """设置反检测爬虫"""
        logger.info("初始化反检测功能...")
        
        # 重试配置
        retry_config = RetryConfig(
            max_attempts=ANTI_DETECTION_CONFIG['retry']['max_attempts'],
            base_delay=ANTI_DETECTION_CONFIG['retry']['base_delay'],
            max_delay=ANTI_DETECTION_CONFIG['retry']['max_delay'],
            strategy=RetryStrategy(ANTI_DETECTION_CONFIG['retry']['strategy']),
            backoff_factor=ANTI_DETECTION_CONFIG['retry']['backoff_factor'],
            jitter=ANTI_DETECTION_CONFIG['retry']['jitter']
        )
        
        # 限流配置
        throttle_config = ThrottleConfig(
            requests_per_second=ANTI_DETECTION_CONFIG['throttle']['requests_per_second'],
            requests_per_minute=ANTI_DETECTION_CONFIG['throttle']['requests_per_minute'],
            requests_per_hour=ANTI_DETECTION_CONFIG['throttle']['requests_per_hour'],
            burst_size=ANTI_DETECTION_CONFIG['throttle']['burst_size'],
            adaptive=ANTI_DETECTION_CONFIG['throttle']['adaptive'],
            min_delay=ANTI_DETECTION_CONFIG['throttle']['min_delay'],
            max_delay=ANTI_DETECTION_CONFIG['throttle']['max_delay']
        )
        
        # 创建反检测爬虫
        anti_spider = AntiDetectionSpider(
            proxy_manager=self.proxy_manager,
            retry_config=retry_config,
            throttle_config=throttle_config.__dict__
        )
        
        logger.success("反检测功能初始化完成")
        return anti_spider
    
    def get_page(self, url, **kwargs):
        """重写get_page方法，使用增强功能"""
        if self.anti_spider:
            # 使用反检测爬虫
            response = self.anti_spider.get(url, **kwargs)
            if response:
                response.encoding = 'utf-8'
            return response
        else:
            # 使用原始方法
            return super().get_page(url, **kwargs)
    
    def crawl_poem_with_stats(self, poem_url):
        """爬取诗词并显示统计信息"""
        logger.info(f"开始爬取诗词: {poem_url}")
        
        start_time = time.time()
        result = self.crawl_poem(poem_url)
        end_time = time.time()
        
        # 显示统计信息
        if self.anti_spider:
            stats = self.anti_spider.get_stats()
            logger.info(f"爬取耗时: {end_time - start_time:.2f}秒")
            logger.info(f"总请求数: {stats['total_requests']}")
            logger.info(f"成功率: {stats['success_rate']:.2%}")
            
            if 'proxy_stats' in stats:
                proxy_stats = stats['proxy_stats']
                logger.info(f"代理使用: {proxy_stats['active_proxies']}/{proxy_stats['total_proxies']}")
        
        return result
    
    def batch_crawl_with_monitoring(self, urls, max_failures=5):
        """批量爬取，带监控功能"""
        logger.info(f"开始批量爬取 {len(urls)} 个URL")
        
        results = []
        failures = 0
        
        for i, url in enumerate(urls, 1):
            try:
                # 检查是否应该继续
                if self.anti_spider and not self.anti_spider.should_continue():
                    logger.warning("检测到异常，暂停爬取")
                    break
                
                # 爬取单个URL
                result = self.crawl_poem(url)
                
                if result:
                    results.append(result)
                    logger.success(f"[{i}/{len(urls)}] 成功: {result['title']}")
                else:
                    failures += 1
                    logger.error(f"[{i}/{len(urls)}] 失败: {url}")
                
                # 检查失败次数
                if failures >= max_failures:
                    logger.error(f"连续失败 {failures} 次，停止爬取")
                    break
                
                # 每10个请求显示一次统计
                if i % 10 == 0 and self.anti_spider:
                    self.anti_spider.print_stats()
                
            except KeyboardInterrupt:
                logger.info("用户中断爬取")
                break
            except Exception as e:
                failures += 1
                logger.error(f"爬取异常: {e}")
        
        # 最终统计
        logger.info(f"批量爬取完成: 成功 {len(results)} 个，失败 {failures} 个")
        
        if self.anti_spider:
            self.anti_spider.print_stats()
        
        return results
    
    def cleanup(self):
        """清理资源"""
        if self.anti_spider:
            self.anti_spider.cleanup()


def demo_basic_usage():
    """基础使用示例"""
    logger.info("=== 基础使用示例 ===")
    
    spider = EnhancedShangShiWenSpider(use_proxy=False, use_advanced_features=True)
    
    try:
        # 测试单个URL
        test_url = "http://shangshiwen.com/67827.html"
        result = spider.crawl_poem_with_stats(test_url)
        
        if result:
            logger.success(f"爬取成功: {result['title']} - {result['author_name']}")
        else:
            logger.error("爬取失败")
    
    finally:
        spider.cleanup()


def demo_proxy_usage():
    """代理使用示例"""
    logger.info("=== 代理使用示例 ===")
    
    spider = EnhancedShangShiWenSpider(use_proxy=True, use_advanced_features=True)
    
    try:
        # 测试多个URL
        test_urls = [
            "http://shangshiwen.com/67827.html",
            "http://shangshiwen.com/67828.html",
            "http://shangshiwen.com/67829.html"
        ]
        
        results = spider.batch_crawl_with_monitoring(test_urls)
        logger.info(f"批量爬取完成，成功 {len(results)} 个")
    
    finally:
        spider.cleanup()


def demo_configuration():
    """配置示例"""
    logger.info("=== 配置示例 ===")
    
    # 显示当前配置
    logger.info("当前反爬配置:")
    for category, config in ANTI_DETECTION_CONFIG.items():
        logger.info(f"  {category}: {config}")


if __name__ == "__main__":
    # 设置日志级别
    logger.remove()
    logger.add(
        "logs/enhanced_spider.log",
        rotation="10 MB",
        retention="7 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}\n"
    )
    
    try:
        # 运行示例
        demo_configuration()
        demo_basic_usage()
        # demo_proxy_usage()  # 需要有效代理时启用
        
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        logger.info("程序结束")

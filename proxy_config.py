# 代理配置文件
# 请根据你的实际情况修改

# 本地代理配置 (推荐)
LOCAL_PROXIES = [
    {'host': '127.0.0.1', 'port': 7890, 'protocol': 'http'},   # Clash
    {'host': '127.0.0.1', 'port': 10809, 'protocol': 'http'},  # V2rayN
    {'host': '127.0.0.1', 'port': 1080, 'protocol': 'http'},   # Shadowsocks
]

# 付费代理配置
PREMIUM_PROXIES = [
    {
        'host': 'your-proxy-server.com',
        'port': 8080,
        'username': 'your_username',
        'password': 'your_password',
        'protocol': 'http'
    }
]

# 使用示例:
# from proxy_config import LOCAL_PROXIES
# from proxy_enhanced_spider import ProxyEnhancedSpider
# 
# spider = ProxyEnhancedSpider(
#     enable_proxy=True,
#     proxy_sources=LOCAL_PROXIES
# )

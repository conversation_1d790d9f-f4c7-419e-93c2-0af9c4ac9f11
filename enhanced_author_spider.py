#!/usr/bin/env python3
"""
增强版诗人爬取脚本 - 集成反爬策略
"""
import os
import re
import time
import random
from bs4 import BeautifulSoup
from loguru import logger
from models import SessionLocal, Author, Dynasty
from urllib.parse import urljoin, urlparse
from enhanced_spider_simple import SimpleEnhancedSpider


class EnhancedAuthorSpider(SimpleEnhancedSpider):
    """增强版诗人爬虫类 - 继承反爬功能"""

    def __init__(self):
        # 调用父类初始化（获得反爬功能）
        super().__init__()

        # 设置基础URL
        self.base_url = "http://shangshiwen.com"

        # 朝代名称到ID的映射（基于数据库中的数据）
        self.dynasty_mapping = {
            '先秦': 1,
            '两汉': 2,
            '魏晋': 3,
            '南北朝': 4,
            '隋代': 5,
            '唐朝': 6,
            '唐代': 6,  # 网站可能使用"唐代"
            '五代': 7,
            '宋代': 8,
            '金朝': 9,
            '元代': 10,
            '明代': 11,
            '清代': 12,
            '近现代': 13,
        }

        # 创建图片保存目录
        self.image_dir = "author_images"
        if not os.path.exists(self.image_dir):
            os.makedirs(self.image_dir)
            logger.info(f"创建图片保存目录: {self.image_dir}")

        # 作者爬取统计
        self.author_count = 0
        self.author_success = 0
        self.author_failures = 0

        logger.info("增强版诗人爬虫初始化完成")

    def parse_author_list_page(self, url):
        """解析诗人列表页面 - 使用增强请求"""
        try:
            logger.info(f"🔍 解析诗人列表页面: {url}")

            # 使用父类的增强请求方法
            response = self.get_page(url)
            if not response:
                logger.error(f"获取诗人列表页面失败: {url}")
                return []

            soup = BeautifulSoup(response.text, 'lxml')
            authors = []

            # 查找诗人链接 - 根据实际网站结构调整选择器
            author_links = soup.find_all(
                'a', href=re.compile(r'/author_\d+\.html'))

            for link in author_links:
                author_name = link.get_text(strip=True)
                author_url = link.get('href')

                if author_name and author_url:
                    authors.append({
                        'name': author_name,
                        'detail_url': author_url,
                        'list_url': url
                    })

            logger.success(f"✅ 从列表页面解析到 {len(authors)} 个诗人")
            return authors

        except Exception as e:
            logger.error(f"❌ 解析诗人列表页面失败: {url} - {e}")
            return []

    def parse_author_detail_page(self, author_info):
        """解析诗人详情页面 - 使用增强请求"""
        try:
            detail_url = urljoin(self.base_url, author_info['detail_url'])
            logger.info(f"🔍 解析诗人详情: {author_info['name']} - {detail_url}")

            # 使用父类的增强请求方法
            response = self.get_page(detail_url)
            if not response:
                logger.error(f"获取诗人详情页面失败: {detail_url}")
                return None

            soup = BeautifulSoup(response.text, 'lxml')

            # 解析诗人信息 - 根据实际网站结构调整
            author_data = {
                'name': author_info['name'],
                'source_url': detail_url,
                'dynasty_id': None,
                'biography': '',
                'birth_year': None,
                'death_year': None,
                'image_url': None,
                'image_filename': None
            }

            # 解析朝代信息
            dynasty_text = self.extract_dynasty_from_page(soup)
            if dynasty_text:
                author_data['dynasty_id'] = self.dynasty_mapping.get(
                    dynasty_text)

            # 解析生平简介
            biography = self.extract_biography_from_page(soup)
            if biography:
                author_data['biography'] = biography

            # 解析头像图片
            image_url = self.extract_image_from_page(soup)
            if image_url:
                author_data['image_url'] = image_url
                # 生成图片文件名
                author_id = self.extract_author_id(detail_url)
                if author_id:
                    author_data['image_filename'] = f"author_{author_id}.jpg"

            logger.success(f"✅ 成功解析诗人: {author_data['name']}")
            return author_data

        except Exception as e:
            logger.error(f"❌ 解析诗人详情失败: {author_info['name']} - {e}")
            return None

    def extract_dynasty_from_page(self, soup):
        """从页面提取朝代信息"""
        try:
            # 尝试多种选择器
            selectors = [
                '.author-dynasty',
                '.dynasty',
                'span:contains("朝代")',
                'div:contains("朝代")'
            ]

            for selector in selectors:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text(strip=True)
                    # 提取朝代名称
                    for dynasty in self.dynasty_mapping.keys():
                        if dynasty in text:
                            return dynasty

            return None
        except Exception as e:
            logger.debug(f"提取朝代信息失败: {e}")
            return None

    def extract_biography_from_page(self, soup):
        """从页面提取生平简介"""
        try:
            # 尝试多种选择器
            selectors = [
                '.author-biography',
                '.biography',
                '.author-intro',
                '.intro',
                'div:contains("简介")',
                'p:contains("生平")'
            ]

            for selector in selectors:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text(strip=True)
                    if len(text) > 20:  # 确保不是标题
                        return text[:500]  # 限制长度

            return None
        except Exception as e:
            logger.debug(f"提取生平简介失败: {e}")
            return None

    def extract_image_from_page(self, soup):
        """从页面提取头像图片URL"""
        try:
            # 尝试多种选择器
            selectors = [
                '.author-avatar img',
                '.author-image img',
                '.avatar img',
                'img[alt*="头像"]',
                'img[src*="author"]'
            ]

            for selector in selectors:
                img = soup.select_one(selector)
                if img and img.get('src'):
                    src = img.get('src')
                    # 转换为绝对URL
                    return urljoin(self.base_url, src)

            return None
        except Exception as e:
            logger.debug(f"提取头像图片失败: {e}")
            return None

    def extract_author_id(self, url):
        """从URL中提取作者ID"""
        try:
            # URL格式: /author_10011.html
            match = re.search(r'/author_(\d+)\.html', url)
            if match:
                return int(match.group(1))
            return None
        except Exception as e:
            logger.error(f"提取作者ID失败: {e}")
            return None

    def download_image(self, image_url, filename):
        """下载并保存图片 - 使用增强请求"""
        try:
            if not image_url or not filename:
                return False

            logger.info(f"📷 下载头像: {filename}")

            # 使用父类的增强请求方法
            response = self.get_page(image_url)
            if not response:
                logger.error(f"下载图片失败: {image_url}")
                return False

            filepath = os.path.join(self.image_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.success(f"✅ 图片下载成功: {filepath}")
            return True

        except Exception as e:
            logger.error(f"❌ 下载图片失败: {e}")
            return False

    def save_author_to_db(self, author_data):
        """保存作者到数据库"""
        db = SessionLocal()
        try:
            # 检查是否已存在
            existing = db.query(Author).filter(
                Author.name == author_data['name']).first()
            if existing:
                logger.info(f"作者已存在: {author_data['name']}")
                return existing.id

            # 创建新作者记录
            author = Author(
                name=author_data['name'],
                dynasty_id=author_data['dynasty_id'],
                biography=author_data['biography'],
                birth_year=author_data.get('birth_year'),
                death_year=author_data.get('death_year'),
                source_url=author_data['source_url']
            )

            db.add(author)
            db.commit()
            db.refresh(author)

            logger.success(f"✅ 成功保存作者: {author_data['name']}")
            return author.id

        except Exception as e:
            db.rollback()
            logger.error(f"❌ 保存作者到数据库失败: {e}")
            return None
        finally:
            db.close()

    def crawl_author(self, author_info):
        """爬取单个作者"""
        try:
            self.author_count += 1
            logger.info(
                f"🕷️  [{self.author_count}] 开始爬取作者: {author_info['name']}")

            # 解析作者详情
            author_data = self.parse_author_detail_page(author_info)
            if not author_data:
                self.author_failures += 1
                return None

            # 下载头像（如果有）
            if author_data['image_url'] and author_data['image_filename']:
                self.download_image(
                    author_data['image_url'], author_data['image_filename'])

            # 保存到数据库
            author_id = self.save_author_to_db(author_data)
            if author_id:
                self.author_success += 1
                logger.success(
                    f"🎉 [{self.author_count}] 成功爬取作者: {author_data['name']}")
                return author_data
            else:
                self.author_failures += 1
                return None

        except Exception as e:
            self.author_failures += 1
            logger.error(f"❌ 爬取作者失败: {author_info['name']} - {e}")
            return None

    def crawl_author_list(self, list_url, max_authors=None):
        """爬取作者列表"""
        logger.info(f"🚀 开始爬取作者列表: {list_url}")

        # 解析列表页面
        authors = self.parse_author_list_page(list_url)
        if not authors:
            logger.error("没有找到作者信息")
            return []

        # 限制数量
        if max_authors:
            authors = authors[:max_authors]

        logger.info(f"准备爬取 {len(authors)} 个作者")

        results = []
        for author_info in authors:
            try:
                result = self.crawl_author(author_info)
                if result:
                    results.append(result)

                # 每10个作者显示一次统计
                if self.author_count % 10 == 0:
                    self.print_author_stats()

            except KeyboardInterrupt:
                logger.info("用户中断爬取")
                break
            except Exception as e:
                logger.error(f"爬取作者异常: {e}")

        # 最终统计
        logger.info(f"🏁 作者列表爬取完成: 成功 {len(results)} 个")
        self.print_author_stats()
        self.print_stats()  # 显示请求统计

        return results

    def print_author_stats(self):
        """打印作者爬取统计"""
        success_rate = self.author_success / \
            self.author_count if self.author_count > 0 else 0

        logger.info("=== 作者爬取统计 ===")
        logger.info(f"总作者数: {self.author_count}")
        logger.info(f"成功数: {self.author_success}")
        logger.info(f"失败数: {self.author_failures}")
        logger.info(f"成功率: {success_rate:.2%}")


def demo_enhanced_author_spider():
    """增强版作者爬虫示例"""
    logger.info("=== 增强版作者爬虫示例 ===")

    spider = EnhancedAuthorSpider()

    try:
        # 测试单个作者爬取（因为作者列表页面不存在）
        test_author_info = {
            'name': '董必武',
            'detail_url': '/author_10011.html'
        }

        logger.info("测试单个作者爬取...")
        result = spider.crawl_author(test_author_info)

        if result:
            logger.success(f"✅ 成功爬取作者: {result['name']}")
        else:
            logger.error("❌ 作者爬取失败")

        # 测试多个作者
        test_authors = [
            {'name': '董必武', 'detail_url': '/author_10011.html'},
            {'name': '测试作者2', 'detail_url': '/author_10012.html'},  # 可能不存在
            {'name': '测试作者3', 'detail_url': '/author_10013.html'},  # 可能不存在
        ]

        logger.info("测试多个作者爬取...")
        results = []
        for author_info in test_authors:
            result = spider.crawl_author(author_info)
            if result:
                results.append(result)

        logger.info(f"示例完成，成功爬取 {len(results)} 个作者")

    except Exception as e:
        logger.error(f"示例运行异常: {e}")

    finally:
        spider.print_stats()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(
        "logs/enhanced_author_spider.log",
        rotation="10 MB",
        retention="7 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}\n"
    )

    try:
        demo_enhanced_author_spider()

    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        logger.info("程序结束")
